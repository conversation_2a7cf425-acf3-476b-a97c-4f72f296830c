"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/8/7 16:24
*  @Project :   ai-test
*  @FileName:   mid_http_client.py
*  @description: 
**************************************
"""
import json
import uuid
from typing import Dict, Any

import requests

from configs.mid_config import MidsceneConfig


class MidsceneHTTPClient:
    """Midscene HTTP 客户端"""

    def __init__(self, config: MidsceneConfig):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

    def check_server_status(self) -> bool:
        """检查服务器状态"""
        try:
            response = self.session.get(f"{self.config.server_url}/status", timeout=5)
            return response.status_code == 200 and response.json().get("status") == "ok"
        except Exception as e:
            print(f"服务器连接失败: {e}")
            return False

    def configure_ai(self) -> bool:
        """配置 AI 模型"""
        ai_config = self.config.to_env_dict()
        if not ai_config:
            return True

        try:
            response = self.session.post(
                f"{self.config.server_url}/config",
                json={"aiConfig": ai_config},
                timeout=10
            )
            return response.status_code == 200
        except Exception as e:
            print(f"AI 配置失败: {e}")
            return False

    def execute_action(self, context: Dict[str, Any], action_type: str,
                       prompt: str, **kwargs) -> Dict[str, Any]:
        """执行 AI 动作"""
        request_id = str(uuid.uuid4())

        payload = {
            "context": json.dumps(context),
            "type": action_type,
            "prompt": prompt,
            "requestId": request_id,
            "deepThink": kwargs.get("deep_think", self.config.deep_think),
            **kwargs
        }

        try:
            response = self.session.post(
                f"{self.config.server_url}/execute",
                json=payload,
                timeout=self.config.timeout
            )

            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}

        except Exception as e:
            return {"error": str(e)}
