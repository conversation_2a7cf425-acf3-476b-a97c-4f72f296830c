"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/8/7 19:17
*  @Project :   ai-test
*  @FileName:   demo.py
*  @description: 
**************************************
"""
import json

from configs.mid_config import MidsceneConfig
from core.mid_case_generator import MidsceneCaseGenerator

# 创建配置
config = MidsceneConfig(
    # model_name='qwen-vl-max-latest',
    model_name='QwQ-32B',
    use_qwen_vl=False,
    # qwen_api_key='sk-68fdd74a22814913b61189c1ea00fb06',
    qwen_api_key='sk-jOLbXwkWAkp5BMVJ88AeC18dEd4547Ab8680BcAf3fFe39D3',
    deep_think=True,
    openai_base_url='http://model.qmai.cn:23000/v1',
)

# 创建生成器
generator = MidsceneCaseGenerator(config)

script = generator.generate_playwright_test(
    instruction="输入邓紫棋，点击百度一下，校验是否有下一页",
    target_url="https://www.baidu.com",
    test_name="baidu_test"
)

print(script)
