"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/8/7 16:25
*  @Project :   ai-test
*  @FileName:   mid_cli_client.py
*  @description: 
**************************************
"""
import subprocess
import os
import json
import shutil
from typing import Dict, Any, Optional

from configs.mid_config import MidsceneConfig


class MidsceneCLIClient:
    """Midscene CLI 客户端"""

    def __init__(self, config: MidsceneConfig):
        self.config = config

    def _find_cli_command(self) -> Optional[str]:
        """查找可用的 CLI 命令"""
        # 尝试不同的命令路径
        commands = [
            "npx @midscene/cli",
            "midscene",
            "node_modules/.bin/midscene",
            "npm run midscene --"
        ]
        
        for cmd in commands:
            try:
                # 测试命令是否可用
                result = subprocess.run(
                    cmd.split() + ["--version"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    shell=True  # Windows 需要 shell=True
                )
                if result.returncode == 0:
                    print(f"找到可用的 CLI 命令: {cmd}")
                    return cmd
            except Exception as e:
                print(f"测试命令 {cmd} 失败: {e}")
                continue
        
        return None

    def _get_env(self) -> Dict[str, str]:
        """获取环境变量"""
        env = os.environ.copy()
        
        # 添加配置的环境变量
        if self.config.openai_api_key:
            env["OPENAI_API_KEY"] = self.config.openai_api_key
        
        if self.config.qwen_api_key:
            env["OPENAI_API_KEY"] = self.config.qwen_api_key
            
        if self.config.openai_base_url:
            env["OPENAI_BASE_URL"] = self.config.openai_base_url
        elif self.config.use_qwen_vl:
            env["OPENAI_BASE_URL"] = self.config.qwen_base_url
            
        if self.config.model_name:
            env["MIDSCENE_MODEL_NAME"] = self.config.model_name
            
        if self.config.use_qwen_vl:
            env["MIDSCENE_USE_QWEN_VL"] = "1"
            
        if self.config.cache_enabled:
            env["MIDSCENE_CACHE"] = "true"
            
        return env

    def execute_cli_command(self, url: str, action: str = None,
                           query: str = None, output_file: str = None,
                           headed: bool = False, sleep: int = 0) -> Dict[str, Any]:
        """执行 CLI 命令"""
        
        # 查找可用的 CLI 命令
        cli_cmd = self._find_cli_command()
        if not cli_cmd:
            return {
                "success": False,
                "error": "未找到可用的 Midscene CLI 命令。请确保已安装 @midscene/cli"
            }

        # 构建命令
        cmd = cli_cmd.split() + ["--url", url]

        if headed:
            cmd.append("--headed")

        if action:
            cmd.extend(["--action", action])

        if query:
            cmd.extend(["--query", query])
            if output_file:
                cmd.extend(["--query-output", output_file])

        if sleep > 0:
            cmd.extend(["--sleep", str(sleep)])

        try:
            print(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                env=self._get_env(),
                timeout=self.config.timeout,
                shell=True,  # Windows 需要
                cwd=os.getcwd()  # 确保在正确的工作目录
            )

            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode,
                "command": " ".join(cmd)
            }

        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": f"命令执行超时 ({self.config.timeout}秒)"
            }
        except FileNotFoundError as e:
            return {
                "success": False,
                "error": f"文件未找到: {e}. 请检查 Node.js 和 @midscene/cli 是否正确安装"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"执行失败: {str(e)}"
            }

    def check_installation(self) -> Dict[str, Any]:
        """检查安装状态"""
        checks = {}
        
        # 检查 Node.js
        try:
            result = subprocess.run(
                ["node", "--version"],
                capture_output=True,
                text=True,
                timeout=10,
                shell=True
            )
            checks["nodejs"] = {
                "installed": result.returncode == 0,
                "version": result.stdout.strip() if result.returncode == 0 else None
            }
        except Exception as e:
            checks["nodejs"] = {"installed": False, "error": str(e)}

        # 检查 npm
        try:
            result = subprocess.run(
                ["npm", "--version"],
                capture_output=True,
                text=True,
                timeout=10,
                shell=True
            )
            checks["npm"] = {
                "installed": result.returncode == 0,
                "version": result.stdout.strip() if result.returncode == 0 else None
            }
        except Exception as e:
            checks["npm"] = {"installed": False, "error": str(e)}

        # 检查 Midscene CLI
        cli_cmd = self._find_cli_command()
        checks["midscene_cli"] = {
            "installed": cli_cmd is not None,
            "command": cli_cmd
        }

        return checks
