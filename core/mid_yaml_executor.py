"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/8/7 18:24
*  @Project :   ai-test
*  @FileName:   mid_yaml_executor.py
*  @description: 
**************************************
"""
import os
import subprocess
import tempfile
from typing import List, Dict, Any

import yaml

from configs.mid_config import MidsceneConfig


class MidsceneYAMLExecutor:
    """Midscene YAML 脚本执行器"""

    def __init__(self, config: MidsceneConfig):
        self.config = config

    def create_yaml_script(self, instruction: str, target_url: str,
                           additional_tasks: List[Dict] = None) -> str:
        """创建 YAML 脚本"""

        script = {
            "web": {
                "url": target_url,
                "viewportWidth": 1280,
                "viewportHeight": 720
            },
            "tasks": [
                {
                    "name": instruction,
                    "flow": [
                        {"ai": instruction}
                    ]
                }
            ]
        }

        # 添加额外任务
        if additional_tasks:
            script["tasks"].extend(additional_tasks)

        return yaml.dump(script, default_flow_style=False, allow_unicode=True)

    def execute_yaml_script(self, yaml_content: str,
                            output_dir: str = None) -> Dict[str, Any]:
        """执行 YAML 脚本"""

        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml',
                                         delete=False, encoding='utf-8') as f:
            f.write(yaml_content)
            yaml_file = f.name

        try:
            # 构建命令
            cmd = ["npx", "@midscene/cli", "--yaml", yaml_file]

            if output_dir:
                cmd.extend(["--output-dir", output_dir])

            # 设置环境变量
            env = os.environ.copy()
            env.update(self.config.to_env_dict())

            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                env=env,
                timeout=self.config.timeout
            )

            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode,
                "yaml_file": yaml_file
            }

        except Exception as e:
            return {"error": str(e), "yaml_file": yaml_file}
        finally:
            # 清理临时文件
            try:
                os.unlink(yaml_file)
            except:
                pass
