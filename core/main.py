#!/usr/bin/env python3
"""
Python 客户端调用 Midscene.js 实现自然语言执行测试
基于 Midscene.js v0.24+ 官方文档实现

支持功能：
1. 通过 HTTP API 调用 Midscene 服务
2. 自然语言转换为测试脚本
3. 支持 Playwright 和 YAML 格式输出
4. 支持 MCP (Model Context Protocol) 调用
5. 支持 Android 自动化
6. 支持缓存和报告生成
"""

import json
from configs.mid_config import MidsceneConfig
from core.mid_case_generator import MidsceneCaseGenerator


def main():
    """示例用法"""

    # 配置 Midscene (使用阿里云通义千问)
    config = MidsceneConfig()

    # 创建测试生成器
    generator = MidsceneCaseGenerator(config)

    # 示例1: 生成 Playwright 测试脚本
    playwright_script = generator.generate_playwright_test(
        instruction="在百度首页搜索'Python教程'并点击第一个结果",
        target_url="https://www.baidu.com",
        test_name="baidu_search_test"
    )

    print("生成的 Playwright 脚本:")
    print(playwright_script)

    # 示例2: 执行自然语言测试 (CLI 模式)
    result = generator.execute_natural_language_test(
        instruction="搜索'天气'并查看结果",
        target_url="https://www.baidu.com",
        execution_mode="cli"
    )

    print("\nCLI 执行结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

    # 示例3: 执行自然语言测试 (YAML 模式)
    yaml_result = generator.execute_natural_language_test(
        instruction="点击登录按钮并填写用户名密码",
        target_url="https://example.com/login",
        execution_mode="yaml"
    )

    print("\nYAML 执行结果:")
    print(json.dumps(yaml_result, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()
