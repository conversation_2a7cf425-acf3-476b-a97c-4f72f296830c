# 测试用例转换流程

## Agent 1: 测试用例解析器 (Test Case Parser)
- 输入：自然语言测试用例
- 输出：结构化测试步骤
- 功能：解析测试目标、前置条件、执行步骤、预期结果

## Agent 2: 脚本结构生成器 (Script Structure Generator)  
- 输入：结构化测试步骤
- 输出：测试脚本框架
- 功能：生成 Playwright 测试文件结构、导入语句、测试套件

## Agent 3: Midscene 指令转换器 (Midscene Command Converter)
- 输入：测试步骤
- 输出：Midscene AI 指令
- 功能：将操作步骤转换为 ai()、aiAssert() 等调用

## Agent 4: 测试数据管理器 (Test Data Manager)
- 输入：测试用例中的数据
- 输出：参数化测试数据
- 功能：提取测试数据、生成数据驱动测试

## Agent 5: 脚本优化器 (Script Optimizer)
- 输入：生成的测试脚本
- 输出：优化后的最终脚本
- 功能：代码优化、重复逻辑合并、错误处理添加