/**
 * 测试用例相关的类型定义
 */

export interface TestStep {
  action: string;
  target?: string;
  value?: string;
  description: string;
}

export interface TestCase {
  id: string;
  module: string;
  subModule: string;
  caseName: string;
  steps: TestStep[];
  expectedResult: string;
  priority: number;
  remarks?: string;
}

export interface ParsedTestSuite {
  cases: TestCase[];
  metadata: {
    totalCases: number;
    source: string;
    parsedAt: Date;
  };
}

export interface TestCaseRow {
  用例编号: string;
  用例模块: string;
  测试项: string;
  用例名称: string;
  操作步骤: string;
  预期结果: string;
  优先级: number;
  备注?: string;
}

export type AIAction = 'aiQuery' | 'aiAssert' | 'aiInput' | 'aiTap' | 'aiScroll' | 'aiWaitFor';

export interface AIStep {
  action: AIAction;
  target: string;
  value?: string;
  description: string;
  timeout?: number;
  options?: Record<string, any>;
}
