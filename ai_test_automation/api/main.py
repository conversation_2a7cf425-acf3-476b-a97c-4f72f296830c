"""
FastAPI主应用
提供REST API接口
"""
from fastapi import FastAPI, UploadFile, File, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import os
import tempfile
import asyncio
from datetime import datetime

from ai_test_automation.core.pipeline import pipeline
from ai_test_automation.config import settings
from ai_test_automation.utils.logger import app_logger
from ai_test_automation.utils.file_handler import FileHandler

# 创建FastAPI应用
app = FastAPI(
    title="AI测试自动化平台",
    description="基于AI的端到端测试自动化解决方案",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
if os.path.exists(settings.output_dir):
    app.mount("/static", StaticFiles(directory=settings.output_dir), name="static")

# 全局变量存储运行中的任务
running_pipelines = {}


class PipelineRequest(BaseModel):
    """流水线请求模型"""
    case_input: Dict[str, Any]
    execution_config: Optional[Dict[str, Any]] = {}
    report_config: Optional[Dict[str, Any]] = {}


class PartialPipelineRequest(BaseModel):
    """部分流水线请求模型"""
    start_stage: str
    input_data: Any
    config: Optional[Dict[str, Any]] = {}


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "AI测试自动化平台",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "pipeline_status": pipeline.get_pipeline_status(),
        "timestamp": datetime.now().isoformat()
    }


@app.post("/api/pipeline/run")
async def run_pipeline(request: PipelineRequest, background_tasks: BackgroundTasks):
    """运行完整流水线"""
    try:
        pipeline_id = f"pipeline_{int(datetime.now().timestamp())}"
        
        # 准备输入配置
        input_config = {
            "case_input": request.case_input,
            "execution_config": request.execution_config,
            "report_config": request.report_config
        }
        
        # 在后台运行流水线
        task = asyncio.create_task(pipeline.run_full_pipeline(input_config))
        running_pipelines[pipeline_id] = {
            "task": task,
            "start_time": datetime.now(),
            "status": "running"
        }
        
        return {
            "pipeline_id": pipeline_id,
            "status": "started",
            "message": "流水线已开始执行"
        }
        
    except Exception as e:
        app_logger.error(f"启动流水线失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/pipeline/{pipeline_id}/status")
async def get_pipeline_status(pipeline_id: str):
    """获取流水线状态"""
    if pipeline_id not in running_pipelines:
        raise HTTPException(status_code=404, detail="流水线不存在")
    
    pipeline_info = running_pipelines[pipeline_id]
    task = pipeline_info["task"]
    
    if task.done():
        try:
            result = task.result()
            pipeline_info["status"] = "completed"
            pipeline_info["result"] = result
        except Exception as e:
            pipeline_info["status"] = "failed"
            pipeline_info["error"] = str(e)
    
    return {
        "pipeline_id": pipeline_id,
        "status": pipeline_info["status"],
        "start_time": pipeline_info["start_time"].isoformat(),
        "result": pipeline_info.get("result"),
        "error": pipeline_info.get("error")
    }


@app.get("/api/pipeline/{pipeline_id}/result")
async def get_pipeline_result(pipeline_id: str):
    """获取流水线结果"""
    if pipeline_id not in running_pipelines:
        raise HTTPException(status_code=404, detail="流水线不存在")
    
    pipeline_info = running_pipelines[pipeline_id]
    task = pipeline_info["task"]
    
    if not task.done():
        raise HTTPException(status_code=202, detail="流水线仍在执行中")
    
    try:
        result = task.result()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/pipeline/partial")
async def run_partial_pipeline(request: PartialPipelineRequest):
    """运行部分流水线"""
    try:
        result = await pipeline.run_partial_pipeline(
            request.start_stage,
            request.input_data,
            request.config
        )
        return {
            "status": "completed",
            "result": result
        }
    except Exception as e:
        app_logger.error(f"部分流水线执行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/upload/testcase")
async def upload_test_case_file(file: UploadFile = File(...)):
    """上传测试用例文件"""
    try:
        # 保存上传的文件
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, file.filename)
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 检测文件类型
        file_type = "auto"
        if file.filename.endswith(('.xlsx', '.xls')):
            file_type = "excel"
        elif file.filename.endswith(('.md', '.markdown')):
            file_type = "markdown"
        elif file.filename.endswith(('.txt', '.csv')):
            file_type = "text"
        
        return {
            "filename": file.filename,
            "file_path": file_path,
            "file_type": file_type,
            "size": len(content),
            "message": "文件上传成功"
        }
        
    except Exception as e:
        app_logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/reports")
async def list_reports():
    """列出所有报告"""
    try:
        reports_dir = os.path.join(settings.output_dir, "reports")
        reports = []
        
        if os.path.exists(reports_dir):
            for report_id in os.listdir(reports_dir):
                report_dir = os.path.join(reports_dir, report_id)
                if os.path.isdir(report_dir):
                    json_file = os.path.join(report_dir, "test_report.json")
                    if os.path.exists(json_file):
                        try:
                            report_data = FileHandler.read_json(json_file)
                            reports.append({
                                "report_id": report_id,
                                "title": report_data.get("report", {}).get("title", ""),
                                "generation_time": report_data.get("generation_time", ""),
                                "total_cases": report_data.get("report", {}).get("total_cases", 0),
                                "pass_rate": report_data.get("report", {}).get("pass_rate", 0)
                            })
                        except:
                            continue
        
        return {"reports": sorted(reports, key=lambda x: x.get("generation_time", ""), reverse=True)}
        
    except Exception as e:
        app_logger.error(f"获取报告列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/reports/{report_id}")
async def get_report(report_id: str):
    """获取指定报告"""
    try:
        report_dir = os.path.join(settings.output_dir, "reports", report_id)
        json_file = os.path.join(report_dir, "test_report.json")
        
        if not os.path.exists(json_file):
            raise HTTPException(status_code=404, detail="报告不存在")
        
        report_data = FileHandler.read_json(json_file)
        return report_data
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"获取报告失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/reports/{report_id}/html")
async def get_report_html(report_id: str):
    """获取HTML报告"""
    try:
        report_dir = os.path.join(settings.output_dir, "reports", report_id)
        html_file = os.path.join(report_dir, "test_report.html")
        
        if not os.path.exists(html_file):
            raise HTTPException(status_code=404, detail="HTML报告不存在")
        
        return FileResponse(html_file, media_type="text/html")
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"获取HTML报告失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/reports/{report_id}/pdf")
async def get_report_pdf(report_id: str):
    """获取PDF报告"""
    try:
        report_dir = os.path.join(settings.output_dir, "reports", report_id)
        pdf_file = os.path.join(report_dir, "test_report.pdf")
        
        if not os.path.exists(pdf_file):
            raise HTTPException(status_code=404, detail="PDF报告不存在")
        
        return FileResponse(pdf_file, media_type="application/pdf")
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"获取PDF报告失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    app_logger.info("AI测试自动化平台启动")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    app_logger.info("AI测试自动化平台关闭")
    await pipeline.cleanup()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
