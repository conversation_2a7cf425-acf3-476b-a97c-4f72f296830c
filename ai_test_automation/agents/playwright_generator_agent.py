"""
Agent3 - Playwright代码生成Agent
将Midscene DSL转换为可执行的Playwright测试代码
"""
from typing import List, Dict, Any
from ai_test_automation.agents.base_agent import BaseAgent
from ai_test_automation.models import MidsceneDSL, PlaywrightScript


class PlaywrightGeneratorAgent(BaseAgent):
    """Playwright代码生成Agent"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("PlaywrightGeneratorAgent", config)
        self.template_imports = [
            'import { expect } from "@playwright/test";',
            'import { test } from "./fixture";'
        ]
    
    async def process(self, input_data: List[MidsceneDSL]) -> List[PlaywrightScript]:
        """处理Midscene DSL，生成Playwright脚本"""
        if not isinstance(input_data, list):
            raise ValueError("输入数据必须是MidsceneDSL列表")
        
        scripts = []
        
        for dsl in input_data:
            self.log_info(f"生成Playwright脚本: {dsl.title}")
            
            script = await self._generate_script_for_dsl(dsl)
            scripts.append(script)
        
        self.log_info(f"成功生成 {len(scripts)} 个Playwright脚本")
        return scripts
    
    async def _generate_script_for_dsl(self, dsl: MidsceneDSL) -> PlaywrightScript:
        """为单个DSL生成Playwright脚本"""
        # 生成导入语句
        imports = self.template_imports.copy()
        
        # 生成设置代码
        setup_code = self._generate_setup_code(dsl)
        
        # 生成测试代码
        test_code = self._generate_test_code(dsl)
        
        # 组合完整脚本
        script_content = self._combine_script_parts(imports, setup_code, test_code)
        
        script = PlaywrightScript(
            case_id=dsl.case_id,
            title=dsl.title,
            script_content=script_content,
            imports=imports,
            setup_code=setup_code,
            test_code=test_code
        )
        
        return script
    
    def _generate_setup_code(self, dsl: MidsceneDSL) -> str:
        """生成设置代码"""
        setup = dsl.setup or {}
        url = setup.get('url', '')
        
        setup_code = f'''test.beforeEach(async ({{ page }}) => {{
  await page.goto("{url}");
  await page.waitForLoadState("networkidle");
}});'''
        
        return setup_code
    
    def _generate_test_code(self, dsl: MidsceneDSL) -> str:
        """生成测试代码"""
        test_name = self._sanitize_test_name(dsl.title)
        
        # 生成测试函数签名
        test_signature = f'''test("{test_name}", async ({{
  ai,
  aiQuery,
  aiAssert,
  aiInput,
  aiTap,
  aiScroll,
  aiWaitFor,
}}) => {{'''
        
        # 生成动作代码
        action_codes = []
        for action in dsl.actions:
            code = self._generate_action_code(action)
            if code:
                action_codes.append(code)
        
        # 生成断言代码
        assertion_codes = []
        for assertion in dsl.assertions:
            code = self._generate_assertion_code(assertion)
            if code:
                assertion_codes.append(code)
        
        # 组合测试代码
        all_codes = action_codes + assertion_codes
        test_body = '\n\n  '.join(all_codes)
        
        test_code = f'''{test_signature}
  {test_body}
}});'''
        
        return test_code
    
    def _generate_action_code(self, action: Dict[str, Any]) -> str:
        """生成动作代码"""
        action_type = action.get('type')
        description = action.get('description', '')
        
        if action_type == 'aiInput':
            return self._generate_input_code(action)
        elif action_type == 'aiTap':
            return self._generate_tap_code(action)
        elif action_type == 'aiScroll':
            return self._generate_scroll_code(action)
        elif action_type == 'aiWaitFor':
            return self._generate_wait_code(action)
        elif action_type == 'aiQuery':
            return self._generate_query_code(action)
        elif action_type == 'aiAssert':
            return self._generate_assert_code(action)
        else:
            return f'// {description}'
    
    def _generate_input_code(self, action: Dict[str, Any]) -> str:
        """生成输入代码"""
        target = action.get('target', '输入框')
        value = action.get('value', '')
        description = action.get('description', '')
        
        return f'''// {description}
  await aiInput('{target}', '{value}');'''
    
    def _generate_tap_code(self, action: Dict[str, Any]) -> str:
        """生成点击代码"""
        target = action.get('target', '按钮')
        description = action.get('description', '')
        
        return f'''// {description}
  await aiTap('{target}');'''
    
    def _generate_scroll_code(self, action: Dict[str, Any]) -> str:
        """生成滚动代码"""
        target = action.get('target', '页面')
        description = action.get('description', '')
        options = action.get('options', {})
        
        direction = options.get('direction', 'down')
        scroll_type = options.get('scrollType', 'once')
        
        options_str = f'''{{
    direction: '{direction}',
    scrollType: '{scroll_type}'
  }}'''
        
        return f'''// {description}
  await aiScroll(
    {options_str},
    '{target}'
  );'''
    
    def _generate_wait_code(self, action: Dict[str, Any]) -> str:
        """生成等待代码"""
        condition = action.get('condition', '')
        description = action.get('description', '')
        timeout = action.get('options', {}).get('timeout', 10000)
        
        return f'''// {description}
  await aiWaitFor('{condition}', {{ timeoutMs: {timeout} }});'''
    
    def _generate_query_code(self, action: Dict[str, Any]) -> str:
        """生成查询代码"""
        query = action.get('query', '')
        description = action.get('description', '')
        
        return f'''// {description}
  const result = await aiQuery('{query}');
  console.log("查询结果：", result);'''
    
    def _generate_assert_code(self, action: Dict[str, Any]) -> str:
        """生成断言代码"""
        condition = action.get('condition', '')
        description = action.get('description', '')
        
        return f'''// {description}
  await aiAssert("{condition}");'''
    
    def _generate_assertion_code(self, assertion: Dict[str, Any]) -> str:
        """生成断言代码"""
        condition = assertion.get('condition', '')
        description = assertion.get('description', '')
        
        return f'''// {description}
  await aiAssert("{condition}");'''
    
    def _combine_script_parts(self, imports: List[str], setup_code: str, test_code: str) -> str:
        """组合脚本各部分"""
        script_parts = []
        
        # 添加导入语句
        script_parts.extend(imports)
        script_parts.append('')
        
        # 添加设置代码
        if setup_code:
            script_parts.append(setup_code)
            script_parts.append('')
        
        # 添加测试代码
        script_parts.append(test_code)
        script_parts.append('')
        
        return '\n'.join(script_parts)
    
    def _sanitize_test_name(self, title: str) -> str:
        """清理测试名称"""
        # 移除特殊字符，保留中文、英文、数字和空格
        import re
        sanitized = re.sub(r'[^\w\s\u4e00-\u9fff]', '', title)
        return sanitized.strip()
    
    def save_script_to_file(self, script: PlaywrightScript, output_dir: str) -> str:
        """保存脚本到文件"""
        import os
        from ai_test_automation.utils.file_handler import FileHandler
        
        # 生成文件名
        safe_title = self._sanitize_test_name(script.title).replace(' ', '_')
        filename = f"{safe_title}_{script.case_id[:8]}.spec.ts"
        file_path = os.path.join(output_dir, filename)
        
        # 保存文件
        FileHandler.write_text(script.script_content, file_path)
        
        self.log_info(f"脚本已保存到: {file_path}")
        return file_path
