"""
Agent6 - 报告生成Agent
聚合数据并生成HTML/PDF格式的测试报告
"""
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
from jinja2 import Template, Environment, FileSystemLoader
from ai_test_automation.agents.base_agent import BaseAgent
from ai_test_automation.models import ExecutionResult, TestReport, TestCaseStatus
from ai_test_automation.config import settings
from ai_test_automation.utils.file_handler import FileHandler


class ReportGeneratorAgent(BaseAgent):
    """报告生成Agent"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("ReportGeneratorAgent", config)
        self.output_dir = config.get('output_dir', settings.output_dir)
        self.template_dir = config.get('template_dir', './templates')
        self.include_screenshots = config.get('include_screenshots', True)
        self.include_videos = config.get('include_videos', True)
        self.generate_pdf = config.get('generate_pdf', True)
        
        # 初始化模板环境
        self._setup_templates()
    
    def _setup_templates(self):
        """设置模板环境"""
        # 创建模板目录
        FileHandler.ensure_directory(self.template_dir)
        
        # 创建默认HTML模板
        self._create_default_html_template()
        
        # 初始化Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(self.template_dir),
            autoescape=True
        )
    
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理执行结果，生成测试报告"""
        execution_results = input_data.get('execution_results', [])
        collection_data = input_data.get('collection_data', {})
        report_config = input_data.get('report_config', {})
        
        if not execution_results:
            raise ValueError("没有提供执行结果数据")
        
        self.log_info(f"开始生成测试报告，结果数量: {len(execution_results)}")
        
        # 创建测试报告对象
        test_report = self._create_test_report(execution_results, report_config)
        
        # 生成报告目录
        report_id = test_report.report_id
        report_dir = os.path.join(self.output_dir, "reports", report_id)
        FileHandler.ensure_directory(report_dir)
        
        # 生成HTML报告
        html_report_path = await self._generate_html_report(
            test_report, execution_results, collection_data, report_dir
        )
        
        # 生成PDF报告（如果启用）
        pdf_report_path = None
        if self.generate_pdf:
            pdf_report_path = await self._generate_pdf_report(html_report_path, report_dir)
        
        # 生成JSON数据文件
        json_report_path = await self._generate_json_report(test_report, execution_results, report_dir)
        
        report_data = {
            'report_id': report_id,
            'report_dir': report_dir,
            'html_report': html_report_path,
            'pdf_report': pdf_report_path,
            'json_report': json_report_path,
            'test_report': test_report,
            'generation_time': datetime.now().isoformat()
        }
        
        self.log_info(f"测试报告生成完成: {report_dir}")
        return report_data
    
    def _create_test_report(self, execution_results: List[ExecutionResult], 
                          config: Dict[str, Any]) -> TestReport:
        """创建测试报告对象"""
        total_cases = len(execution_results)
        passed_cases = len([r for r in execution_results if r.status == TestCaseStatus.PASSED])
        failed_cases = len([r for r in execution_results if r.status == TestCaseStatus.FAILED])
        skipped_cases = len([r for r in execution_results if r.status == TestCaseStatus.SKIPPED])
        error_cases = len([r for r in execution_results if r.status == TestCaseStatus.ERROR])
        
        # 计算通过率
        pass_rate = (passed_cases / total_cases * 100) if total_cases > 0 else 0
        
        # 计算时间
        start_times = [r.start_time for r in execution_results if r.start_time]
        end_times = [r.end_time for r in execution_results if r.end_time]
        
        start_time = min(start_times) if start_times else datetime.now()
        end_time = max(end_times) if end_times else datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        report_id = config.get('report_id', f"report_{int(datetime.now().timestamp())}")
        title = config.get('title', f"自动化测试报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return TestReport(
            report_id=report_id,
            title=title,
            total_cases=total_cases,
            passed_cases=passed_cases,
            failed_cases=failed_cases,
            skipped_cases=skipped_cases,
            error_cases=error_cases,
            pass_rate=round(pass_rate, 2),
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            results=execution_results
        )
    
    async def _generate_html_report(self, test_report: TestReport, 
                                  execution_results: List[ExecutionResult],
                                  collection_data: Dict[str, Any],
                                  report_dir: str) -> str:
        """生成HTML报告"""
        try:
            # 加载模板
            template = self.jinja_env.get_template('test_report.html')
            
            # 准备模板数据
            template_data = {
                'report': test_report,
                'results': execution_results,
                'collection_data': collection_data,
                'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'include_screenshots': self.include_screenshots,
                'include_videos': self.include_videos
            }
            
            # 渲染HTML
            html_content = template.render(**template_data)
            
            # 保存HTML文件
            html_file = os.path.join(report_dir, 'test_report.html')
            FileHandler.write_text(html_content, html_file)
            
            # 复制静态资源
            await self._copy_static_resources(report_dir)
            
            self.log_info(f"HTML报告生成完成: {html_file}")
            return html_file
            
        except Exception as e:
            self.log_error(f"生成HTML报告失败: {e}")
            raise
    
    async def _generate_pdf_report(self, html_report_path: str, report_dir: str) -> Optional[str]:
        """生成PDF报告"""
        try:
            # 使用weasyprint生成PDF
            from weasyprint import HTML, CSS
            
            pdf_file = os.path.join(report_dir, 'test_report.pdf')
            
            # 读取HTML内容
            html_content = FileHandler.read_text(html_report_path)
            
            # 生成PDF
            HTML(string=html_content, base_url=report_dir).write_pdf(pdf_file)
            
            self.log_info(f"PDF报告生成完成: {pdf_file}")
            return pdf_file
            
        except ImportError:
            self.log_warning("weasyprint未安装，跳过PDF生成")
            return None
        except Exception as e:
            self.log_error(f"生成PDF报告失败: {e}")
            return None
    
    async def _generate_json_report(self, test_report: TestReport, 
                                  execution_results: List[ExecutionResult],
                                  report_dir: str) -> str:
        """生成JSON数据报告"""
        try:
            json_data = {
                'report': test_report.dict(),
                'results': [result.dict() for result in execution_results],
                'generation_time': datetime.now().isoformat()
            }
            
            json_file = os.path.join(report_dir, 'test_report.json')
            FileHandler.write_json(json_data, json_file)
            
            self.log_info(f"JSON报告生成完成: {json_file}")
            return json_file
            
        except Exception as e:
            self.log_error(f"生成JSON报告失败: {e}")
            raise
    
    async def _copy_static_resources(self, report_dir: str):
        """复制静态资源文件"""
        try:
            # 创建静态资源目录
            static_dir = os.path.join(report_dir, 'static')
            FileHandler.ensure_directory(static_dir)
            
            # 创建基础CSS文件
            css_content = self._get_default_css()
            css_file = os.path.join(static_dir, 'style.css')
            FileHandler.write_text(css_content, css_file)
            
            # 创建基础JS文件
            js_content = self._get_default_js()
            js_file = os.path.join(static_dir, 'script.js')
            FileHandler.write_text(js_content, js_file)
            
        except Exception as e:
            self.log_error(f"复制静态资源失败: {e}")
    
    def _create_default_html_template(self):
        """创建默认HTML模板"""
        template_file = os.path.join(self.template_dir, 'test_report.html')
        
        if not os.path.exists(template_file):
            template_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report.title }}</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>{{ report.title }}</h1>
            <div class="report-meta">
                <span>生成时间: {{ generation_time }}</span>
                <span>报告ID: {{ report.report_id }}</span>
            </div>
        </header>
        
        <section class="summary">
            <h2>执行摘要</h2>
            <div class="summary-cards">
                <div class="card total">
                    <h3>总用例数</h3>
                    <span class="number">{{ report.total_cases }}</span>
                </div>
                <div class="card passed">
                    <h3>通过</h3>
                    <span class="number">{{ report.passed_cases }}</span>
                </div>
                <div class="card failed">
                    <h3>失败</h3>
                    <span class="number">{{ report.failed_cases }}</span>
                </div>
                <div class="card error">
                    <h3>错误</h3>
                    <span class="number">{{ report.error_cases }}</span>
                </div>
                <div class="card pass-rate">
                    <h3>通过率</h3>
                    <span class="number">{{ report.pass_rate }}%</span>
                </div>
            </div>
            <div class="time-info">
                <p>开始时间: {{ report.start_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                <p>结束时间: {{ report.end_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                <p>总耗时: {{ "%.2f"|format(report.duration) }} 秒</p>
            </div>
        </section>
        
        <section class="results">
            <h2>详细结果</h2>
            <div class="results-table">
                <table>
                    <thead>
                        <tr>
                            <th>用例ID</th>
                            <th>状态</th>
                            <th>开始时间</th>
                            <th>耗时(秒)</th>
                            <th>重试次数</th>
                            <th>错误信息</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for result in results %}
                        <tr class="result-row {{ result.status.value }}">
                            <td>{{ result.case_id[:8] }}...</td>
                            <td><span class="status-badge {{ result.status.value }}">{{ result.status.value }}</span></td>
                            <td>{{ result.start_time.strftime('%H:%M:%S') if result.start_time else '-' }}</td>
                            <td>{{ "%.2f"|format(result.duration) if result.duration else '-' }}</td>
                            <td>{{ result.retry_count }}</td>
                            <td class="error-message">{{ result.error_message[:100] + '...' if result.error_message and result.error_message|length > 100 else result.error_message or '-' }}</td>
                            <td>
                                {% if result.screenshots %}
                                <button onclick="showScreenshots('{{ result.case_id }}')">查看截图</button>
                                {% endif %}
                                {% if result.videos %}
                                <button onclick="showVideos('{{ result.case_id }}')">查看视频</button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </section>
    </div>
    
    <script src="static/script.js"></script>
</body>
</html>'''
            
            FileHandler.write_text(template_content, template_file)
    
    def _get_default_css(self) -> str:
        """获取默认CSS样式"""
        return '''
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.report-header {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.report-header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.report-meta {
    color: #7f8c8d;
    font-size: 14px;
}

.report-meta span {
    margin-right: 20px;
}

.summary {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.summary h2 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #ddd;
}

.card.total { border-left-color: #3498db; }
.card.passed { border-left-color: #27ae60; }
.card.failed { border-left-color: #e74c3c; }
.card.error { border-left-color: #f39c12; }
.card.pass-rate { border-left-color: #9b59b6; }

.card h3 {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 10px;
}

.card .number {
    font-size: 32px;
    font-weight: bold;
    color: #2c3e50;
}

.time-info {
    background: #ecf0f1;
    padding: 15px;
    border-radius: 4px;
}

.results {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.results h2 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.results-table {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.passed { background: #d4edda; color: #155724; }
.status-badge.failed { background: #f8d7da; color: #721c24; }
.status-badge.error { background: #fff3cd; color: #856404; }
.status-badge.skipped { background: #e2e3e5; color: #383d41; }

.error-message {
    max-width: 300px;
    word-wrap: break-word;
}

button {
    background: #3498db;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin-right: 5px;
}

button:hover {
    background: #2980b9;
}
'''
    
    def _get_default_js(self) -> str:
        """获取默认JavaScript"""
        return '''
function showScreenshots(caseId) {
    alert('查看截图功能: ' + caseId);
}

function showVideos(caseId) {
    alert('查看视频功能: ' + caseId);
}

// 表格排序功能
document.addEventListener('DOMContentLoaded', function() {
    const table = document.querySelector('table');
    const headers = table.querySelectorAll('th');
    
    headers.forEach((header, index) => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', () => sortTable(index));
    });
});

function sortTable(columnIndex) {
    const table = document.querySelector('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        if (!isNaN(aText) && !isNaN(bText)) {
            return parseFloat(aText) - parseFloat(bText);
        }
        
        return aText.localeCompare(bText);
    });
    
    rows.forEach(row => tbody.appendChild(row));
}
'''
