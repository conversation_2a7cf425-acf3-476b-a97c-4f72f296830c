"""
Agent4 - 批量执行控制Agent
支持并发控制、重试机制和执行状态管理
"""
import asyncio
import subprocess
import time
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
from ai_test_automation.agents.base_agent import BaseAgent
from ai_test_automation.models import PlaywrightScript, ExecutionResult, TestCaseStatus, BatchExecution
from ai_test_automation.config import settings
from ai_test_automation.utils.file_handler import FileHandler
import os
import json
from datetime import datetime


class BatchExecutorAgent(BaseAgent):
    """批量执行控制Agent"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("BatchExecutorAgent", config)
        self.max_concurrent = config.get('max_concurrent', settings.max_concurrent_tests)
        self.retry_count = config.get('retry_count', settings.retry_count)
        self.timeout = config.get('timeout', settings.test_timeout)
        self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent)
        self.running_tasks = {}
    
    async def process(self, input_data: Dict[str, Any]) -> List[ExecutionResult]:
        """处理批量执行请求"""
        scripts = input_data.get('scripts', [])
        batch_config = input_data.get('batch_config', {})
        
        if not scripts:
            raise ValueError("没有提供要执行的脚本")
        
        # 创建批次执行配置
        batch_execution = BatchExecution(
            batch_id=batch_config.get('batch_id', f"batch_{int(time.time())}"),
            case_ids=[script.case_id for script in scripts],
            max_concurrent=batch_config.get('max_concurrent', self.max_concurrent),
            retry_count=batch_config.get('retry_count', self.retry_count),
            timeout=batch_config.get('timeout', self.timeout),
            browser_config=batch_config.get('browser_config', {})
        )
        
        self.log_info(f"开始批量执行，批次ID: {batch_execution.batch_id}")
        self.log_info(f"脚本数量: {len(scripts)}, 最大并发: {batch_execution.max_concurrent}")
        
        # 执行脚本
        results = await self._execute_scripts_batch(scripts, batch_execution)
        
        self.log_info(f"批量执行完成，成功: {len([r for r in results if r.status == TestCaseStatus.PASSED])}, "
                     f"失败: {len([r for r in results if r.status == TestCaseStatus.FAILED])}")
        
        return results
    
    async def _execute_scripts_batch(self, scripts: List[PlaywrightScript], 
                                   batch_config: BatchExecution) -> List[ExecutionResult]:
        """批量执行脚本"""
        semaphore = asyncio.Semaphore(batch_config.max_concurrent)
        tasks = []
        
        for script in scripts:
            task = asyncio.create_task(
                self._execute_single_script_with_semaphore(script, batch_config, semaphore)
            )
            tasks.append(task)
            self.running_tasks[script.case_id] = task
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        execution_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 创建错误结果
                error_result = ExecutionResult(
                    case_id=scripts[i].case_id,
                    status=TestCaseStatus.ERROR,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    error_message=str(result)
                )
                execution_results.append(error_result)
            else:
                execution_results.append(result)
        
        return execution_results
    
    async def _execute_single_script_with_semaphore(self, script: PlaywrightScript, 
                                                  batch_config: BatchExecution,
                                                  semaphore: asyncio.Semaphore) -> ExecutionResult:
        """使用信号量控制的单脚本执行"""
        async with semaphore:
            return await self._execute_single_script(script, batch_config)
    
    async def _execute_single_script(self, script: PlaywrightScript, 
                                   batch_config: BatchExecution) -> ExecutionResult:
        """执行单个脚本"""
        start_time = datetime.now()
        
        self.log_info(f"开始执行脚本: {script.title}")
        
        # 创建执行结果
        result = ExecutionResult(
            case_id=script.case_id,
            status=TestCaseStatus.RUNNING,
            start_time=start_time
        )
        
        # 保存脚本到临时文件
        script_path = await self._save_script_to_temp(script)
        
        try:
            # 执行脚本（带重试）
            for attempt in range(batch_config.retry_count + 1):
                try:
                    execution_result = await self._run_playwright_script(script_path, batch_config)
                    
                    if execution_result['success']:
                        result.status = TestCaseStatus.PASSED
                        break
                    else:
                        if attempt < batch_config.retry_count:
                            self.log_warning(f"脚本执行失败，第 {attempt + 1} 次重试: {script.title}")
                            result.retry_count = attempt + 1
                            await asyncio.sleep(2)  # 重试间隔
                        else:
                            result.status = TestCaseStatus.FAILED
                            result.error_message = execution_result.get('error', '执行失败')
                
                except Exception as e:
                    if attempt < batch_config.retry_count:
                        self.log_warning(f"脚本执行异常，第 {attempt + 1} 次重试: {script.title}, 错误: {e}")
                        result.retry_count = attempt + 1
                        await asyncio.sleep(2)
                    else:
                        result.status = TestCaseStatus.ERROR
                        result.error_message = str(e)
            
            # 收集执行结果数据
            result.screenshots = execution_result.get('screenshots', [])
            result.videos = execution_result.get('videos', [])
            result.logs = execution_result.get('logs', [])
            
        except Exception as e:
            result.status = TestCaseStatus.ERROR
            result.error_message = str(e)
            self.log_error(f"脚本执行异常: {script.title}, 错误: {e}")
        
        finally:
            # 清理临时文件
            try:
                os.remove(script_path)
            except:
                pass
            
            # 更新结束时间和持续时间
            result.end_time = datetime.now()
            result.duration = (result.end_time - result.start_time).total_seconds()
            
            # 从运行任务中移除
            self.running_tasks.pop(script.case_id, None)
        
        self.log_info(f"脚本执行完成: {script.title}, 状态: {result.status}")
        return result

    async def _save_script_to_temp(self, script: PlaywrightScript) -> str:
        """保存脚本到临时文件"""
        temp_dir = settings.temp_dir
        FileHandler.ensure_directory(temp_dir)

        # 生成临时文件名
        filename = f"temp_{script.case_id}_{int(time.time())}.spec.ts"
        script_path = os.path.join(temp_dir, filename)

        # 保存脚本内容
        FileHandler.write_text(script.script_content, script_path)

        return script_path

    async def _run_playwright_script(self, script_path: str,
                                   batch_config: BatchExecution) -> Dict[str, Any]:
        """运行Playwright脚本"""
        try:
            # 构建命令
            cmd = [
                "npx", "playwright", "test", script_path,
                "--reporter=json",
                f"--timeout={batch_config.timeout * 1000}"  # 转换为毫秒
            ]

            # 添加浏览器配置
            browser_config = batch_config.browser_config or {}
            if browser_config.get('headless', True):
                cmd.append("--headed=false")
            else:
                cmd.append("--headed=true")

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=os.getcwd()
            )

            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=batch_config.timeout
            )

            # 解析结果
            result = {
                'success': process.returncode == 0,
                'stdout': stdout.decode('utf-8') if stdout else '',
                'stderr': stderr.decode('utf-8') if stderr else '',
                'return_code': process.returncode,
                'screenshots': [],
                'videos': [],
                'logs': []
            }

            # 解析JSON报告
            if result['stdout']:
                try:
                    json_report = json.loads(result['stdout'])
                    result.update(self._parse_playwright_report(json_report))
                except json.JSONDecodeError:
                    pass

            if not result['success']:
                result['error'] = result['stderr'] or '执行失败'

            return result

        except asyncio.TimeoutError:
            return {
                'success': False,
                'error': f'脚本执行超时 ({batch_config.timeout}秒)',
                'screenshots': [],
                'videos': [],
                'logs': []
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'screenshots': [],
                'videos': [],
                'logs': []
            }

    def _parse_playwright_report(self, json_report: Dict[str, Any]) -> Dict[str, Any]:
        """解析Playwright JSON报告"""
        result = {
            'screenshots': [],
            'videos': [],
            'logs': []
        }

        # 提取测试结果
        suites = json_report.get('suites', [])
        for suite in suites:
            specs = suite.get('specs', [])
            for spec in specs:
                tests = spec.get('tests', [])
                for test in tests:
                    results = test.get('results', [])
                    for test_result in results:
                        # 提取附件
                        attachments = test_result.get('attachments', [])
                        for attachment in attachments:
                            if attachment.get('contentType') == 'image/png':
                                result['screenshots'].append(attachment.get('path', ''))
                            elif attachment.get('contentType') == 'video/webm':
                                result['videos'].append(attachment.get('path', ''))

                        # 提取错误信息
                        if test_result.get('error'):
                            result['logs'].append(test_result['error'].get('message', ''))

        return result

    async def get_execution_status(self, case_id: str) -> Optional[str]:
        """获取执行状态"""
        task = self.running_tasks.get(case_id)
        if task is None:
            return None

        if task.done():
            return "completed"
        else:
            return "running"

    async def cancel_execution(self, case_id: str) -> bool:
        """取消执行"""
        task = self.running_tasks.get(case_id)
        if task and not task.done():
            task.cancel()
            self.running_tasks.pop(case_id, None)
            self.log_info(f"已取消执行: {case_id}")
            return True
        return False

    async def get_running_tasks(self) -> List[str]:
        """获取正在运行的任务列表"""
        return [case_id for case_id, task in self.running_tasks.items() if not task.done()]

    def cleanup(self):
        """清理资源"""
        # 取消所有运行中的任务
        for task in self.running_tasks.values():
            if not task.done():
                task.cancel()

        # 关闭线程池
        self.executor.shutdown(wait=True)

        self.log_info("批量执行器已清理")
