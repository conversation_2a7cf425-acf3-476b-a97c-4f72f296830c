"""
Agent2 - Midscene DSL生成Agent
将结构化数据转换为Midscene DSL格式
"""
from typing import List, Dict, Any
from ai_test_automation.agents.base_agent import BaseAgent
from ai_test_automation.models import TestCase, TestStep, ActionType, MidsceneDSL


class MidsceneDSLAgent(BaseAgent):
    """Midscene DSL生成Agent"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("MidsceneDSLAgent", config)
        self.action_mapping = {
            ActionType.AI_QUERY: self._generate_query_action,
            ActionType.AI_ASSERT: self._generate_assert_action,
            ActionType.AI_INPUT: self._generate_input_action,
            ActionType.AI_TAP: self._generate_tap_action,
            ActionType.AI_SCROLL: self._generate_scroll_action,
            ActionType.AI_WAIT_FOR: self._generate_wait_action
        }
    
    async def process(self, input_data: List[TestCase]) -> List[MidsceneDSL]:
        """处理测试用例，生成Midscene DSL"""
        if not isinstance(input_data, list):
            raise ValueError("输入数据必须是TestCase列表")
        
        dsl_list = []
        
        for test_case in input_data:
            self.log_info(f"生成DSL: {test_case.title}")
            
            dsl = await self._generate_dsl_for_case(test_case)
            dsl_list.append(dsl)
        
        self.log_info(f"成功生成 {len(dsl_list)} 个DSL")
        return dsl_list
    
    async def _generate_dsl_for_case(self, test_case: TestCase) -> MidsceneDSL:
        """为单个测试用例生成DSL"""
        # 生成设置部分
        setup = self._generate_setup(test_case)
        
        # 生成动作列表
        actions = []
        assertions = []
        
        for step in test_case.steps:
            if step.action_type == ActionType.AI_ASSERT:
                assertion = self._generate_assertion(step)
                assertions.append(assertion)
            else:
                action = self._generate_action(step)
                actions.append(action)
        
        dsl = MidsceneDSL(
            case_id=test_case.case_id,
            title=test_case.title,
            setup=setup,
            actions=actions,
            assertions=assertions
        )
        
        return dsl
    
    def _generate_setup(self, test_case: TestCase) -> Dict[str, Any]:
        """生成设置部分"""
        setup = {
            "title": test_case.title,
            "description": test_case.description or "",
            "url": test_case.url or "",
            "browser": {
                "type": "chromium",
                "headless": True,
                "viewport": {
                    "width": 1920,
                    "height": 1080
                }
            },
            "timeout": 30000,
            "retries": 3
        }
        
        if test_case.preconditions:
            setup["preconditions"] = test_case.preconditions
        
        return setup
    
    def _generate_action(self, step: TestStep) -> Dict[str, Any]:
        """生成动作"""
        generator = self.action_mapping.get(step.action_type)
        if not generator:
            raise ValueError(f"不支持的动作类型: {step.action_type}")
        
        return generator(step)
    
    def _generate_assertion(self, step: TestStep) -> Dict[str, Any]:
        """生成断言"""
        return {
            "type": "assert",
            "description": step.description,
            "condition": step.expected_result or step.description,
            "timeout": step.timeout or 10000
        }
    
    def _generate_query_action(self, step: TestStep) -> Dict[str, Any]:
        """生成查询动作"""
        return {
            "type": "aiQuery",
            "description": step.description,
            "query": step.target or step.description,
            "options": step.options or {}
        }
    
    def _generate_assert_action(self, step: TestStep) -> Dict[str, Any]:
        """生成断言动作"""
        return {
            "type": "aiAssert",
            "description": step.description,
            "condition": step.expected_result or step.description,
            "timeout": step.timeout or 10000
        }
    
    def _generate_input_action(self, step: TestStep) -> Dict[str, Any]:
        """生成输入动作"""
        return {
            "type": "aiInput",
            "description": step.description,
            "target": step.target or "输入框",
            "value": step.value or "",
            "options": {
                "clear": True,
                "delay": 100,
                **(step.options or {})
            }
        }
    
    def _generate_tap_action(self, step: TestStep) -> Dict[str, Any]:
        """生成点击动作"""
        return {
            "type": "aiTap",
            "description": step.description,
            "target": step.target or "按钮",
            "options": {
                "timeout": step.timeout or 10000,
                **(step.options or {})
            }
        }
    
    def _generate_scroll_action(self, step: TestStep) -> Dict[str, Any]:
        """生成滚动动作"""
        options = step.options or {}
        
        # 从描述中推断滚动方向
        direction = "down"
        if "上" in step.description or "向上" in step.description:
            direction = "up"
        elif "下" in step.description or "向下" in step.description:
            direction = "down"
        
        return {
            "type": "aiScroll",
            "description": step.description,
            "target": step.target or "页面",
            "options": {
                "direction": options.get("direction", direction),
                "scrollType": options.get("scrollType", "once"),
                "distance": options.get("distance", 500),
                **(step.options or {})
            }
        }
    
    def _generate_wait_action(self, step: TestStep) -> Dict[str, Any]:
        """生成等待动作"""
        return {
            "type": "aiWaitFor",
            "description": step.description,
            "condition": step.target or step.description,
            "options": {
                "timeout": step.timeout or 30000,
                **(step.options or {})
            }
        }
    
    def to_json(self, dsl_list: List[MidsceneDSL]) -> List[Dict[str, Any]]:
        """转换为JSON格式"""
        return [dsl.dict() for dsl in dsl_list]
    
    def to_yaml_string(self, dsl: MidsceneDSL) -> str:
        """转换为YAML字符串格式"""
        yaml_content = f"""# {dsl.title}
case_id: {dsl.case_id}
title: "{dsl.title}"

setup:
  url: "{dsl.setup.get('url', '')}"
  browser:
    type: {dsl.setup.get('browser', {}).get('type', 'chromium')}
    headless: {str(dsl.setup.get('browser', {}).get('headless', True)).lower()}
    viewport:
      width: {dsl.setup.get('browser', {}).get('viewport', {}).get('width', 1920)}
      height: {dsl.setup.get('browser', {}).get('viewport', {}).get('height', 1080)}
  timeout: {dsl.setup.get('timeout', 30000)}
  retries: {dsl.setup.get('retries', 3)}

actions:"""
        
        for action in dsl.actions:
            yaml_content += f"""
  - type: {action['type']}
    description: "{action['description']}"
    target: "{action.get('target', '')}"
"""
            if action.get('value'):
                yaml_content += f"    value: \"{action['value']}\"\n"
            
            if action.get('options'):
                yaml_content += "    options:\n"
                for key, value in action['options'].items():
                    if isinstance(value, str):
                        yaml_content += f"      {key}: \"{value}\"\n"
                    else:
                        yaml_content += f"      {key}: {value}\n"
        
        if dsl.assertions:
            yaml_content += "\nassertions:"
            for assertion in dsl.assertions:
                yaml_content += f"""
  - type: {assertion['type']}
    description: "{assertion['description']}"
    condition: "{assertion['condition']}"
    timeout: {assertion.get('timeout', 10000)}
"""
        
        return yaml_content
