"""
Agent1 - 用例解析Agent
支持Excel/文本/Markdown格式的测试用例输入，输出结构化数据
"""
import re
import uuid
from typing import List, Dict, Any, Union
import pandas as pd
from ai_test_automation.agents.base_agent import BaseAgent
from ai_test_automation.models import TestCase, TestStep, ActionType
from ai_test_automation.utils.file_handler import FileHandler


class CaseParserAgent(BaseAgent):
    """用例解析Agent"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("CaseParserAgent", config)
        self.action_keywords = {
            "点击": ActionType.AI_TAP,
            "输入": ActionType.AI_INPUT,
            "滚动": ActionType.AI_SCROLL,
            "等待": ActionType.AI_WAIT_FOR,
            "查询": ActionType.AI_QUERY,
            "验证": ActionType.AI_ASSERT,
            "断言": ActionType.AI_ASSERT,
            "检查": ActionType.AI_ASSERT
        }
    
    async def process(self, input_data: Dict[str, Any]) -> List[TestCase]:
        """处理输入数据，解析测试用例"""
        file_path = input_data.get("file_path")
        file_type = input_data.get("file_type", "auto")
        
        if not file_path:
            raise ValueError("缺少文件路径")
        
        # 自动检测文件类型
        if file_type == "auto":
            file_type = self._detect_file_type(file_path)
        
        self.log_info(f"开始解析文件: {file_path}, 类型: {file_type}")
        
        if file_type == "excel":
            return await self._parse_excel(file_path, input_data.get("sheet_name"))
        elif file_type == "markdown":
            return await self._parse_markdown(file_path)
        elif file_type == "text":
            return await self._parse_text(file_path)
        else:
            raise ValueError(f"不支持的文件类型: {file_type}")
    
    def _detect_file_type(self, file_path: str) -> str:
        """自动检测文件类型"""
        extension = FileHandler.get_file_extension(file_path)
        if extension in ['.xlsx', '.xls']:
            return "excel"
        elif extension in ['.md', '.markdown']:
            return "markdown"
        elif extension in ['.txt', '.csv']:
            return "text"
        else:
            raise ValueError(f"无法识别的文件扩展名: {extension}")
    
    async def _parse_excel(self, file_path: str, sheet_name: str = None) -> List[TestCase]:
        """解析Excel文件"""
        try:
            df = FileHandler.read_excel(file_path, sheet_name)
            test_cases = []
            
            # 标准化列名
            df.columns = df.columns.str.strip()
            required_columns = ['用例标题', '操作步骤', '预期结果']
            
            # 检查必需列
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Excel文件缺少必需列: {missing_columns}")
            
            for index, row in df.iterrows():
                if pd.isna(row['用例标题']) or not str(row['用例标题']).strip():
                    continue
                
                case_id = str(uuid.uuid4())
                title = str(row['用例标题']).strip()
                description = str(row.get('用例描述', '')).strip() if not pd.isna(row.get('用例描述')) else None
                url = str(row.get('测试URL', '')).strip() if not pd.isna(row.get('测试URL')) else None
                
                # 解析操作步骤
                steps_text = str(row['操作步骤']).strip() if not pd.isna(row['操作步骤']) else ""
                expected_text = str(row['预期结果']).strip() if not pd.isna(row['预期结果']) else ""
                
                steps = self._parse_steps(steps_text, expected_text)
                
                # 解析标签
                tags = []
                if not pd.isna(row.get('标签')):
                    tags = [tag.strip() for tag in str(row['标签']).split(',') if tag.strip()]
                
                priority = str(row.get('优先级', 'medium')).strip().lower() if not pd.isna(row.get('优先级')) else 'medium'
                
                test_case = TestCase(
                    case_id=case_id,
                    title=title,
                    description=description,
                    url=url,
                    steps=steps,
                    tags=tags,
                    priority=priority
                )
                
                test_cases.append(test_case)
                self.log_info(f"解析用例: {title}")
            
            self.log_info(f"成功解析 {len(test_cases)} 个测试用例")
            return test_cases
            
        except Exception as e:
            self.log_error(f"解析Excel文件失败: {e}")
            raise
    
    async def _parse_markdown(self, file_path: str) -> List[TestCase]:
        """解析Markdown文件"""
        try:
            md_data = FileHandler.read_markdown(file_path)
            test_cases = []
            
            # 从表格中解析用例
            for table in md_data['tables']:
                for row in table:
                    if '用例标题' in row and row['用例标题'].strip():
                        case_id = str(uuid.uuid4())
                        title = row['用例标题'].strip()
                        description = row.get('用例描述', '').strip() or None
                        url = row.get('测试URL', '').strip() or None
                        
                        steps_text = row.get('操作步骤', '').strip()
                        expected_text = row.get('预期结果', '').strip()
                        
                        steps = self._parse_steps(steps_text, expected_text)
                        
                        tags = []
                        if row.get('标签'):
                            tags = [tag.strip() for tag in row['标签'].split(',') if tag.strip()]
                        
                        priority = row.get('优先级', 'medium').strip().lower()
                        
                        test_case = TestCase(
                            case_id=case_id,
                            title=title,
                            description=description,
                            url=url,
                            steps=steps,
                            tags=tags,
                            priority=priority
                        )
                        
                        test_cases.append(test_case)
                        self.log_info(f"解析用例: {title}")
            
            self.log_info(f"成功解析 {len(test_cases)} 个测试用例")
            return test_cases
            
        except Exception as e:
            self.log_error(f"解析Markdown文件失败: {e}")
            raise
    
    async def _parse_text(self, file_path: str) -> List[TestCase]:
        """解析文本文件"""
        try:
            content = FileHandler.read_text(file_path)
            test_cases = []
            
            # 按空行分割用例
            case_blocks = [block.strip() for block in content.split('\n\n') if block.strip()]
            
            for block in case_blocks:
                lines = [line.strip() for line in block.split('\n') if line.strip()]
                if not lines:
                    continue
                
                case_id = str(uuid.uuid4())
                title = lines[0]  # 第一行作为标题
                
                # 查找操作步骤和预期结果
                steps_text = ""
                expected_text = ""
                
                for i, line in enumerate(lines[1:], 1):
                    if line.startswith('操作步骤:') or line.startswith('步骤:'):
                        steps_text = line.split(':', 1)[1].strip()
                    elif line.startswith('预期结果:') or line.startswith('期望:'):
                        expected_text = line.split(':', 1)[1].strip()
                    elif not steps_text and i == 1:
                        steps_text = line  # 如果没有明确标识，第二行作为步骤
                    elif not expected_text and i == 2:
                        expected_text = line  # 第三行作为预期结果
                
                steps = self._parse_steps(steps_text, expected_text)
                
                test_case = TestCase(
                    case_id=case_id,
                    title=title,
                    steps=steps
                )
                
                test_cases.append(test_case)
                self.log_info(f"解析用例: {title}")
            
            self.log_info(f"成功解析 {len(test_cases)} 个测试用例")
            return test_cases
            
        except Exception as e:
            self.log_error(f"解析文本文件失败: {e}")
            raise
    
    def _parse_steps(self, steps_text: str, expected_text: str = "") -> List[TestStep]:
        """解析操作步骤"""
        steps = []
        
        if not steps_text:
            return steps
        
        # 按分号或换行分割步骤
        step_items = re.split(r'[;\n]', steps_text)
        
        for i, step_text in enumerate(step_items):
            step_text = step_text.strip()
            if not step_text:
                continue
            
            step_id = str(uuid.uuid4())
            action_type = self._detect_action_type(step_text)
            
            # 提取目标和值
            target, value = self._extract_target_and_value(step_text, action_type)
            
            step = TestStep(
                step_id=step_id,
                action_type=action_type,
                description=step_text,
                target=target,
                value=value
            )
            
            steps.append(step)
        
        # 添加断言步骤
        if expected_text:
            step_id = str(uuid.uuid4())
            step = TestStep(
                step_id=step_id,
                action_type=ActionType.AI_ASSERT,
                description=f"验证: {expected_text}",
                expected_result=expected_text
            )
            steps.append(step)
        
        return steps
    
    def _detect_action_type(self, step_text: str) -> ActionType:
        """检测动作类型"""
        for keyword, action_type in self.action_keywords.items():
            if keyword in step_text:
                return action_type
        
        # 默认返回查询类型
        return ActionType.AI_QUERY
    
    def _extract_target_and_value(self, step_text: str, action_type: ActionType) -> tuple:
        """提取目标元素和值"""
        target = None
        value = None
        
        if action_type == ActionType.AI_INPUT:
            # 输入操作：提取输入框和输入值
            match = re.search(r'在(.+?)中?输入(.+)', step_text)
            if match:
                target = match.group(1).strip()
                value = match.group(2).strip()
        
        elif action_type == ActionType.AI_TAP:
            # 点击操作：提取点击目标
            match = re.search(r'点击(.+)', step_text)
            if match:
                target = match.group(1).strip()
        
        elif action_type == ActionType.AI_WAIT_FOR:
            # 等待操作：提取等待条件
            match = re.search(r'等待(.+)', step_text)
            if match:
                target = match.group(1).strip()
        
        return target, value
