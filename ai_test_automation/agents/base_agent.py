"""
基础Agent类
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from ai_test_automation.utils.logger import app_logger


class BaseAgent(ABC):
    """基础Agent抽象类"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.config = config or {}
        self.logger = app_logger
        self.logger.info(f"初始化Agent: {self.name}")
    
    @abstractmethod
    async def process(self, input_data: Any) -> Any:
        """处理输入数据"""
        pass
    
    def validate_input(self, input_data: Any) -> bool:
        """验证输入数据"""
        return True
    
    def log_info(self, message: str):
        """记录信息日志"""
        self.logger.info(f"[{self.name}] {message}")
    
    def log_error(self, message: str):
        """记录错误日志"""
        self.logger.error(f"[{self.name}] {message}")
    
    def log_warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(f"[{self.name}] {message}")
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(key, default)
