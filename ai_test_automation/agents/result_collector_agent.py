"""
Agent5 - 结果收集Agent
收集执行日志、截图、视频等测试结果数据
"""
import os
import shutil
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
from ai_test_automation.agents.base_agent import BaseAgent
from ai_test_automation.models import ExecutionResult, TestCaseStatus
from ai_test_automation.config import settings
from ai_test_automation.utils.file_handler import FileHandler


class ResultCollectorAgent(BaseAgent):
    """结果收集Agent"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("ResultCollectorAgent", config)
        self.output_dir = config.get('output_dir', settings.output_dir)
        self.collect_screenshots = config.get('collect_screenshots', True)
        self.collect_videos = config.get('collect_videos', True)
        self.collect_logs = config.get('collect_logs', True)
        self.compress_results = config.get('compress_results', False)
    
    async def process(self, input_data: List[ExecutionResult]) -> Dict[str, Any]:
        """处理执行结果，收集相关数据"""
        if not isinstance(input_data, list):
            raise ValueError("输入数据必须是ExecutionResult列表")
        
        self.log_info(f"开始收集 {len(input_data)} 个执行结果")
        
        # 创建收集目录
        collection_id = f"collection_{int(datetime.now().timestamp())}"
        collection_dir = os.path.join(self.output_dir, "collections", collection_id)
        FileHandler.ensure_directory(collection_dir)
        
        # 收集结果
        collected_data = {
            'collection_id': collection_id,
            'collection_time': datetime.now().isoformat(),
            'total_results': len(input_data),
            'collection_dir': collection_dir,
            'results': [],
            'summary': self._generate_summary(input_data),
            'artifacts': {
                'screenshots': [],
                'videos': [],
                'logs': [],
                'reports': []
            }
        }
        
        # 逐个收集结果
        for result in input_data:
            collected_result = await self._collect_single_result(result, collection_dir)
            collected_data['results'].append(collected_result)
            
            # 更新artifacts统计
            collected_data['artifacts']['screenshots'].extend(collected_result.get('screenshots', []))
            collected_data['artifacts']['videos'].extend(collected_result.get('videos', []))
            collected_data['artifacts']['logs'].extend(collected_result.get('logs', []))
        
        # 生成收集报告
        report_path = await self._generate_collection_report(collected_data, collection_dir)
        collected_data['artifacts']['reports'].append(report_path)
        
        # 压缩结果（如果启用）
        if self.compress_results:
            archive_path = await self._compress_collection(collection_dir)
            collected_data['archive_path'] = archive_path
        
        self.log_info(f"结果收集完成，收集目录: {collection_dir}")
        return collected_data
    
    async def _collect_single_result(self, result: ExecutionResult, collection_dir: str) -> Dict[str, Any]:
        """收集单个执行结果"""
        self.log_info(f"收集结果: {result.case_id}")
        
        # 创建用例专用目录
        case_dir = os.path.join(collection_dir, f"case_{result.case_id}")
        FileHandler.ensure_directory(case_dir)
        
        collected_result = {
            'case_id': result.case_id,
            'status': result.status.value,
            'start_time': result.start_time.isoformat() if result.start_time else None,
            'end_time': result.end_time.isoformat() if result.end_time else None,
            'duration': result.duration,
            'retry_count': result.retry_count,
            'error_message': result.error_message,
            'case_dir': case_dir,
            'screenshots': [],
            'videos': [],
            'logs': []
        }
        
        # 收集截图
        if self.collect_screenshots and result.screenshots:
            collected_result['screenshots'] = await self._collect_screenshots(
                result.screenshots, case_dir
            )
        
        # 收集视频
        if self.collect_videos and result.videos:
            collected_result['videos'] = await self._collect_videos(
                result.videos, case_dir
            )
        
        # 收集日志
        if self.collect_logs and result.logs:
            collected_result['logs'] = await self._collect_logs(
                result.logs, case_dir
            )
        
        # 生成用例结果文件
        result_file = os.path.join(case_dir, "result.json")
        FileHandler.write_json(collected_result, result_file)
        
        return collected_result
    
    async def _collect_screenshots(self, screenshots: List[str], case_dir: str) -> List[str]:
        """收集截图文件"""
        screenshot_dir = os.path.join(case_dir, "screenshots")
        FileHandler.ensure_directory(screenshot_dir)
        
        collected_screenshots = []
        
        for i, screenshot_path in enumerate(screenshots):
            if os.path.exists(screenshot_path):
                # 生成新文件名
                filename = f"screenshot_{i+1}_{int(datetime.now().timestamp())}.png"
                new_path = os.path.join(screenshot_dir, filename)
                
                try:
                    # 复制文件
                    shutil.copy2(screenshot_path, new_path)
                    collected_screenshots.append(new_path)
                    self.log_info(f"收集截图: {new_path}")
                except Exception as e:
                    self.log_error(f"复制截图失败: {screenshot_path}, 错误: {e}")
            else:
                self.log_warning(f"截图文件不存在: {screenshot_path}")
        
        return collected_screenshots
    
    async def _collect_videos(self, videos: List[str], case_dir: str) -> List[str]:
        """收集视频文件"""
        video_dir = os.path.join(case_dir, "videos")
        FileHandler.ensure_directory(video_dir)
        
        collected_videos = []
        
        for i, video_path in enumerate(videos):
            if os.path.exists(video_path):
                # 生成新文件名
                ext = os.path.splitext(video_path)[1] or '.webm'
                filename = f"video_{i+1}_{int(datetime.now().timestamp())}{ext}"
                new_path = os.path.join(video_dir, filename)
                
                try:
                    # 复制文件
                    shutil.copy2(video_path, new_path)
                    collected_videos.append(new_path)
                    self.log_info(f"收集视频: {new_path}")
                except Exception as e:
                    self.log_error(f"复制视频失败: {video_path}, 错误: {e}")
            else:
                self.log_warning(f"视频文件不存在: {video_path}")
        
        return collected_videos
    
    async def _collect_logs(self, logs: List[str], case_dir: str) -> List[str]:
        """收集日志信息"""
        log_dir = os.path.join(case_dir, "logs")
        FileHandler.ensure_directory(log_dir)
        
        collected_logs = []
        
        # 创建日志文件
        log_file = os.path.join(log_dir, f"execution_log_{int(datetime.now().timestamp())}.txt")
        
        try:
            log_content = []
            log_content.append(f"执行日志 - {datetime.now().isoformat()}")
            log_content.append("=" * 50)
            
            for i, log_entry in enumerate(logs):
                log_content.append(f"\n[{i+1}] {log_entry}")
            
            FileHandler.write_text('\n'.join(log_content), log_file)
            collected_logs.append(log_file)
            self.log_info(f"收集日志: {log_file}")
            
        except Exception as e:
            self.log_error(f"保存日志失败: {e}")
        
        return collected_logs
    
    def _generate_summary(self, results: List[ExecutionResult]) -> Dict[str, Any]:
        """生成结果摘要"""
        total = len(results)
        passed = len([r for r in results if r.status == TestCaseStatus.PASSED])
        failed = len([r for r in results if r.status == TestCaseStatus.FAILED])
        error = len([r for r in results if r.status == TestCaseStatus.ERROR])
        skipped = len([r for r in results if r.status == TestCaseStatus.SKIPPED])
        
        # 计算总执行时间
        total_duration = sum([r.duration or 0 for r in results])
        
        # 计算通过率
        pass_rate = (passed / total * 100) if total > 0 else 0
        
        return {
            'total_cases': total,
            'passed_cases': passed,
            'failed_cases': failed,
            'error_cases': error,
            'skipped_cases': skipped,
            'pass_rate': round(pass_rate, 2),
            'total_duration': round(total_duration, 2),
            'average_duration': round(total_duration / total, 2) if total > 0 else 0
        }
    
    async def _generate_collection_report(self, collected_data: Dict[str, Any], 
                                        collection_dir: str) -> str:
        """生成收集报告"""
        report_file = os.path.join(collection_dir, "collection_report.json")
        
        try:
            FileHandler.write_json(collected_data, report_file)
            self.log_info(f"生成收集报告: {report_file}")
            return report_file
        except Exception as e:
            self.log_error(f"生成收集报告失败: {e}")
            return ""
    
    async def _compress_collection(self, collection_dir: str) -> str:
        """压缩收集结果"""
        try:
            import zipfile
            
            archive_name = f"{os.path.basename(collection_dir)}.zip"
            archive_path = os.path.join(os.path.dirname(collection_dir), archive_name)
            
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(collection_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, collection_dir)
                        zipf.write(file_path, arc_name)
            
            self.log_info(f"压缩完成: {archive_path}")
            return archive_path
            
        except Exception as e:
            self.log_error(f"压缩失败: {e}")
            return ""
    
    async def get_collection_info(self, collection_id: str) -> Optional[Dict[str, Any]]:
        """获取收集信息"""
        collection_dir = os.path.join(self.output_dir, "collections", collection_id)
        report_file = os.path.join(collection_dir, "collection_report.json")
        
        if os.path.exists(report_file):
            try:
                return FileHandler.read_json(report_file)
            except Exception as e:
                self.log_error(f"读取收集报告失败: {e}")
        
        return None
    
    async def list_collections(self) -> List[Dict[str, Any]]:
        """列出所有收集"""
        collections_dir = os.path.join(self.output_dir, "collections")
        collections = []
        
        if os.path.exists(collections_dir):
            for collection_id in os.listdir(collections_dir):
                collection_info = await self.get_collection_info(collection_id)
                if collection_info:
                    collections.append({
                        'collection_id': collection_id,
                        'collection_time': collection_info.get('collection_time'),
                        'total_results': collection_info.get('total_results'),
                        'summary': collection_info.get('summary')
                    })
        
        return sorted(collections, key=lambda x: x.get('collection_time', ''), reverse=True)
