"""
配置管理模块
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置"""
    
    # API配置
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    
    # 数据库配置
    database_url: str = Field(default="sqlite:///./test_automation.db", env="DATABASE_URL")
    
    # 执行配置
    max_concurrent_tests: int = Field(default=5, env="MAX_CONCURRENT_TESTS")
    test_timeout: int = Field(default=300, env="TEST_TIMEOUT")  # 5分钟
    retry_count: int = Field(default=3, env="RETRY_COUNT")
    
    # 存储配置
    output_dir: str = Field(default="./output", env="OUTPUT_DIR")
    temp_dir: str = Field(default="./temp", env="TEMP_DIR")
    
    # 浏览器配置
    browser_type: str = Field(default="chromium", env="BROWSER_TYPE")
    headless: bool = Field(default=True, env="HEADLESS")
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="./logs/app.log", env="LOG_FILE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()


def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.output_dir,
        settings.temp_dir,
        os.path.dirname(settings.log_file),
        f"{settings.output_dir}/reports",
        f"{settings.output_dir}/screenshots",
        f"{settings.output_dir}/videos",
        f"{settings.output_dir}/scripts"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


# 初始化目录
ensure_directories()
