# AI测试自动化框架

基于AI的端到端测试自动化解决方案，支持从测试用例到自动化脚本的完整流程。

## 功能特性

### 🎯 核心功能
- **智能用例解析**: 支持Excel、Markdown、文本格式的测试用例输入
- **DSL自动生成**: 将测试用例转换为Midscene DSL格式
- **脚本自动生成**: 生成可执行的Playwright测试脚本
- **批量并发执行**: 支持并发控制、重试机制和状态管理
- **结果智能收集**: 自动收集截图、视频、日志等测试数据
- **报告自动生成**: 生成HTML/PDF格式的详细测试报告

### 🏗️ 架构设计
```
┌──────────────────────────────────────────────────────────┐ 
│                   阶段 1：用例 → 脚本生成                 │ 
└──────────────────────────────────────────────────────────┘ 
   用户用例输入（Excel / 文本 / Markdown） 
                  │ 
                  ▼ 
     ┌───────────────┐ 
     │ Agent1         │ 
     │ 用例解析        │ 
     │ 输出结构化数据  │ 
     └───────┬────────┘ 
             ▼ 
     ┌───────────────┐ 
     │ Agent2         │ 
     │ 生成 Midscene  │ 
     │ 场景描述 (DSL) │ 
     └───────┬────────┘ 
             ▼ 
     ┌───────────────┐ 
     │ Agent3         │ 
     │ Midscene →     │ 
     │ Playwright代码 │ 
     └───────┬────────┘ 
             ▼ 
     脚本存储（代码仓库/临时目录） 
 
 
┌──────────────────────────────────────────────────────────┐ 
│                阶段 2：批量执行与结果收集                │ 
└──────────────────────────────────────────────────────────┘ 
             ▼ 
     ┌───────────────┐ 
     │ Agent4         │ 
     │ 批量执行脚本   │ 
     │ 控制并发&重试  │ 
     └───────┬────────┘ 
             ▼ 
     ┌───────────────┐ 
     │ Agent5         │ 
     │ 收集执行结果   │ 
     │ 日志/截图/视频 │ 
     └───────┬────────┘ 
             ▼ 
 
 
┌──────────────────────────────────────────────────────────┐ 
│                   阶段 3：报告生成                       │ 
└──────────────────────────────────────────────────────────┘ 
             ▼ 
     ┌───────────────┐ 
     │ Agent6         │ 
     │ 聚合数据生成   │ 
     │ HTML/PDF报告   │ 
     └───────┬────────┘ 
             ▼ 
     最终测试报告（通过率、失败详情、截图链接）
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境

复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：
```env
OPENAI_API_KEY=your_openai_api_key_here
OUTPUT_DIR=./output
MAX_CONCURRENT_TESTS=5
```

### 3. 安装Playwright浏览器

```bash
playwright install chromium
```

### 4. 运行示例

#### 命令行方式
```bash
# 运行完整流水线
python -m ai_test_automation.cli run examples/sample_testcases.xlsx

# 仅解析测试用例
python -m ai_test_automation.cli parse examples/sample_testcases.md

# 查看系统状态
python -m ai_test_automation.cli status
```

#### API方式
```bash
# 启动API服务
python -m ai_test_automation.api.main

# 访问API文档
# http://localhost:8000/docs
```

#### Python代码方式
```python
import asyncio
from ai_test_automation.core.pipeline import pipeline

async def main():
    input_config = {
        "case_input": {
            "file_path": "examples/sample_testcases.xlsx",
            "file_type": "excel"
        },
        "execution_config": {
            "max_concurrent": 3,
            "retry_count": 2
        },
        "report_config": {
            "generate_pdf": True
        }
    }
    
    result = await pipeline.run_full_pipeline(input_config)
    print(f"执行完成，报告路径: {result['report_data']['report_dir']}")

asyncio.run(main())
```

## 测试用例格式

### Excel格式
| 用例标题 | 用例描述 | 测试URL | 操作步骤 | 预期结果 | 标签 | 优先级 |
|---------|---------|---------|---------|---------|------|-------|
| 百度搜索测试 | 验证搜索功能 | https://www.baidu.com | 在搜索框输入'测试';点击搜索按钮 | 显示搜索结果 | 搜索 | high |

### Markdown格式
```markdown
| 用例标题 | 操作步骤 | 预期结果 |
|---------|---------|---------|
| 百度搜索测试 | 在搜索框输入'测试';点击搜索按钮 | 显示搜索结果 |
```

### 文本格式
```
百度搜索测试
操作步骤: 在搜索框输入'测试';点击搜索按钮
预期结果: 显示搜索结果
```

## 支持的操作类型

框架支持以下Midscene操作类型：

- **aiInput**: 输入文本到指定元素
- **aiTap**: 点击指定元素
- **aiScroll**: 滚动页面或元素
- **aiWaitFor**: 等待指定条件
- **aiQuery**: 查询页面信息
- **aiAssert**: 断言验证

## API接口

### 主要端点

- `POST /api/pipeline/run` - 运行完整流水线
- `GET /api/pipeline/{id}/status` - 获取执行状态
- `GET /api/pipeline/{id}/result` - 获取执行结果
- `POST /api/upload/testcase` - 上传测试用例文件
- `GET /api/reports` - 获取报告列表
- `GET /api/reports/{id}/html` - 获取HTML报告

详细API文档请访问: http://localhost:8000/docs

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|-------|------|-------|
| OPENAI_API_KEY | OpenAI API密钥 | - |
| MAX_CONCURRENT_TESTS | 最大并发测试数 | 5 |
| TEST_TIMEOUT | 测试超时时间(秒) | 300 |
| RETRY_COUNT | 重试次数 | 3 |
| OUTPUT_DIR | 输出目录 | ./output |
| BROWSER_TYPE | 浏览器类型 | chromium |
| HEADLESS | 无头模式 | true |

### 流水线配置

```python
config = {
    "case_parser": {
        # 用例解析配置
    },
    "dsl_generator": {
        # DSL生成配置
    },
    "script_generator": {
        # 脚本生成配置
    },
    "batch_executor": {
        "max_concurrent": 5,
        "retry_count": 3,
        "timeout": 300
    },
    "result_collector": {
        "collect_screenshots": True,
        "collect_videos": True,
        "compress_results": False
    },
    "report_generator": {
        "generate_pdf": True,
        "include_screenshots": True
    }
}
```

## 开发指南

### 项目结构

```
ai_test_automation/
├── agents/                 # Agent模块
│   ├── case_parser_agent.py
│   ├── midscene_dsl_agent.py
│   ├── playwright_generator_agent.py
│   ├── batch_executor_agent.py
│   ├── result_collector_agent.py
│   └── report_generator_agent.py
├── core/                   # 核心模块
│   └── pipeline.py
├── api/                    # API模块
│   └── main.py
├── utils/                  # 工具模块
│   ├── logger.py
│   └── file_handler.py
├── examples/               # 示例文件
├── tests/                  # 测试文件
├── models.py              # 数据模型
├── config.py              # 配置管理
└── cli.py                 # 命令行工具
```

### 扩展Agent

继承BaseAgent类来创建自定义Agent：

```python
from ai_test_automation.agents.base_agent import BaseAgent

class CustomAgent(BaseAgent):
    def __init__(self, config=None):
        super().__init__("CustomAgent", config)
    
    async def process(self, input_data):
        # 实现处理逻辑
        return processed_data
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_pipeline.py -v

# 运行测试并生成覆盖率报告
pytest --cov=ai_test_automation
```

## 故障排除

### 常见问题

1. **Playwright安装问题**
   ```bash
   playwright install chromium
   ```

2. **权限问题**
   ```bash
   chmod +x ai_test_automation/cli.py
   ```

3. **依赖冲突**
   ```bash
   pip install --upgrade -r requirements.txt
   ```

### 日志查看

日志文件位置: `./logs/app.log`

```bash
tail -f logs/app.log
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。
