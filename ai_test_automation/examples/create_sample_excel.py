"""
创建示例Excel测试用例文件
"""
import pandas as pd
import os

def create_sample_excel():
    """创建示例Excel文件"""
    
    # 示例测试用例数据
    test_cases = [
        {
            "用例标题": "百度搜索功能测试",
            "用例描述": "验证百度搜索功能是否正常工作",
            "测试URL": "https://www.baidu.com",
            "操作步骤": "在搜索框中输入'笔记本电脑';点击百度一下按钮;等待搜索结果加载;滚动页面查看更多结果",
            "预期结果": "显示相关的搜索结果列表",
            "标签": "搜索,基础功能",
            "优先级": "high"
        },
        {
            "用例标题": "淘宝商品搜索测试",
            "用例描述": "测试淘宝商品搜索和筛选功能",
            "测试URL": "https://www.taobao.com",
            "操作步骤": "在搜索框输入'手机';点击搜索按钮;等待商品列表加载;点击价格筛选;选择价格范围",
            "预期结果": "显示符合条件的商品列表",
            "标签": "电商,搜索,筛选",
            "优先级": "medium"
        },
        {
            "用例标题": "京东登录功能测试",
            "用例描述": "验证京东用户登录流程",
            "测试URL": "https://www.jd.com",
            "操作步骤": "点击登录按钮;输入用户名;输入密码;点击登录确认",
            "预期结果": "成功登录并跳转到用户中心",
            "标签": "登录,用户认证",
            "优先级": "high"
        },
        {
            "用例标题": "知乎文章浏览测试",
            "用例描述": "测试知乎文章浏览和互动功能",
            "测试URL": "https://www.zhihu.com",
            "操作步骤": "搜索技术相关话题;点击第一个搜索结果;滚动阅读文章内容;点击点赞按钮",
            "预期结果": "文章正常显示，点赞功能正常",
            "标签": "内容浏览,社交",
            "优先级": "low"
        },
        {
            "用例标题": "GitHub仓库搜索测试",
            "用例描述": "测试GitHub代码仓库搜索功能",
            "测试URL": "https://github.com",
            "操作步骤": "在搜索框输入'python web framework';选择仓库搜索;点击搜索;查看搜索结果;点击第一个仓库",
            "预期结果": "显示相关的Python Web框架仓库列表",
            "标签": "代码搜索,开发工具",
            "优先级": "medium"
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(test_cases)
    
    # 确保目录存在
    os.makedirs("ai_test_automation/examples", exist_ok=True)
    
    # 保存为Excel文件
    excel_file = "ai_test_automation/examples/sample_testcases.xlsx"
    df.to_excel(excel_file, index=False, sheet_name="测试用例")
    
    print(f"示例Excel文件已创建: {excel_file}")
    print(f"包含 {len(test_cases)} 个测试用例")
    
    return excel_file

if __name__ == "__main__":
    create_sample_excel()
