"""
主入口文件
提供多种启动方式
"""
import asyncio
import sys
import os
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_test_automation.core.pipeline import pipeline
from ai_test_automation.utils.logger import app_logger
from ai_test_automation.config import settings


async def run_example():
    """运行示例流水线"""
    print("🚀 AI测试自动化框架示例")
    print("=" * 50)
    
    # 创建示例Excel文件
    try:
        from ai_test_automation.examples.create_sample_excel import create_sample_excel
        excel_file = create_sample_excel()
        print(f"✓ 创建示例Excel文件: {excel_file}")
    except Exception as e:
        print(f"✗ 创建示例文件失败: {e}")
        return
    
    # 配置流水线输入
    input_config = {
        "case_input": {
            "file_path": excel_file,
            "file_type": "excel",
            "sheet_name": "测试用例"
        },
        "execution_config": {
            "max_concurrent": 2,
            "retry_count": 1,
            "timeout": 60  # 缩短超时时间用于演示
        },
        "report_config": {
            "title": "AI测试自动化框架演示报告",
            "generate_pdf": False  # 跳过PDF生成以加快演示
        }
    }
    
    print("\n📋 开始执行流水线...")
    print(f"输入文件: {excel_file}")
    print(f"最大并发: {input_config['execution_config']['max_concurrent']}")
    
    try:
        # 运行流水线（仅到脚本生成阶段，跳过实际执行）
        print("\n🔍 阶段1: 解析测试用例...")
        test_cases = await pipeline.parse_test_cases(input_config["case_input"])
        print(f"✓ 成功解析 {len(test_cases)} 个测试用例")
        
        print("\n🔧 阶段2: 生成Midscene DSL...")
        dsl_list = await pipeline.generate_dsl(test_cases)
        print(f"✓ 成功生成 {len(dsl_list)} 个DSL")
        
        print("\n📝 阶段3: 生成Playwright脚本...")
        scripts = await pipeline.generate_scripts(dsl_list)
        print(f"✓ 成功生成 {len(scripts)} 个脚本")
        
        # 显示生成的脚本示例
        if scripts:
            print(f"\n📄 脚本示例 (前100字符):")
            print("-" * 50)
            print(scripts[0].script_content[:100] + "...")
            print("-" * 50)
        
        # 保存脚本到输出目录
        script_dir = os.path.join(settings.output_dir, "generated_scripts")
        os.makedirs(script_dir, exist_ok=True)
        
        for i, script in enumerate(scripts):
            script_file = os.path.join(script_dir, f"test_case_{i+1}.spec.ts")
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(script.script_content)
            print(f"✓ 脚本已保存: {script_file}")
        
        print(f"\n🎉 演示完成！")
        print(f"生成的脚本保存在: {script_dir}")
        print(f"日志文件: {settings.log_file}")
        
        # 显示用例摘要
        print(f"\n📊 用例摘要:")
        for i, case in enumerate(test_cases, 1):
            print(f"  {i}. {case.title} ({len(case.steps)} 个步骤)")
        
    except Exception as e:
        print(f"\n✗ 流水线执行失败: {e}")
        app_logger.error(f"演示执行失败: {e}")


async def run_api_server():
    """启动API服务器"""
    print("🌐 启动API服务器...")
    
    try:
        import uvicorn
        from ai_test_automation.api.main import app
        
        print(f"API服务器启动在: http://localhost:8000")
        print(f"API文档地址: http://localhost:8000/docs")
        
        uvicorn.run(app, host="0.0.0.0", port=8000)
        
    except ImportError:
        print("✗ uvicorn未安装，请运行: pip install uvicorn")
    except Exception as e:
        print(f"✗ API服务器启动失败: {e}")


def run_cli():
    """运行命令行工具"""
    print("💻 启动命令行工具...")
    
    try:
        from ai_test_automation.cli import app as cli_app
        cli_app()
    except Exception as e:
        print(f"✗ 命令行工具启动失败: {e}")


def show_help():
    """显示帮助信息"""
    print("""
🤖 AI测试自动化框架

使用方式:
  python main.py [command]

命令:
  example    运行示例流水线演示
  api        启动API服务器
  cli        启动命令行工具
  help       显示此帮助信息

示例:
  python main.py example     # 运行演示
  python main.py api         # 启动API服务
  python main.py cli run examples/sample_testcases.xlsx

更多信息请查看 README.md
""")


async def main():
    """主函数"""
    # 获取命令行参数
    command = sys.argv[1] if len(sys.argv) > 1 else "help"
    
    if command == "example":
        await run_example()
    elif command == "api":
        await run_api_server()
    elif command == "cli":
        run_cli()
    elif command == "help":
        show_help()
    else:
        print(f"未知命令: {command}")
        show_help()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        sys.exit(1)
