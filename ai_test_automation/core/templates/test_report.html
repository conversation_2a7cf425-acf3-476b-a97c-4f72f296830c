<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report.title }}</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>{{ report.title }}</h1>
            <div class="report-meta">
                <span>生成时间: {{ generation_time }}</span>
                <span>报告ID: {{ report.report_id }}</span>
            </div>
        </header>
        
        <section class="summary">
            <h2>执行摘要</h2>
            <div class="summary-cards">
                <div class="card total">
                    <h3>总用例数</h3>
                    <span class="number">{{ report.total_cases }}</span>
                </div>
                <div class="card passed">
                    <h3>通过</h3>
                    <span class="number">{{ report.passed_cases }}</span>
                </div>
                <div class="card failed">
                    <h3>失败</h3>
                    <span class="number">{{ report.failed_cases }}</span>
                </div>
                <div class="card error">
                    <h3>错误</h3>
                    <span class="number">{{ report.error_cases }}</span>
                </div>
                <div class="card pass-rate">
                    <h3>通过率</h3>
                    <span class="number">{{ report.pass_rate }}%</span>
                </div>
            </div>
            <div class="time-info">
                <p>开始时间: {{ report.start_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                <p>结束时间: {{ report.end_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                <p>总耗时: {{ "%.2f"|format(report.duration) }} 秒</p>
            </div>
        </section>
        
        <section class="results">
            <h2>详细结果</h2>
            <div class="results-table">
                <table>
                    <thead>
                        <tr>
                            <th>用例ID</th>
                            <th>状态</th>
                            <th>开始时间</th>
                            <th>耗时(秒)</th>
                            <th>重试次数</th>
                            <th>错误信息</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for result in results %}
                        <tr class="result-row {{ result.status.value }}">
                            <td>{{ result.case_id[:8] }}...</td>
                            <td><span class="status-badge {{ result.status.value }}">{{ result.status.value }}</span></td>
                            <td>{{ result.start_time.strftime('%H:%M:%S') if result.start_time else '-' }}</td>
                            <td>{{ "%.2f"|format(result.duration) if result.duration else '-' }}</td>
                            <td>{{ result.retry_count }}</td>
                            <td class="error-message">{{ result.error_message[:100] + '...' if result.error_message and result.error_message|length > 100 else result.error_message or '-' }}</td>
                            <td>
                                {% if result.screenshots %}
                                <button onclick="showScreenshots('{{ result.case_id }}')">查看截图</button>
                                {% endif %}
                                {% if result.videos %}
                                <button onclick="showVideos('{{ result.case_id }}')">查看视频</button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </section>
    </div>
    
    <script src="static/script.js"></script>
</body>
</html>