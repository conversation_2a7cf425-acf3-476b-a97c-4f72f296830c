"""
测试自动化流水线
整合所有Agent，提供端到端的测试自动化流程
"""
from typing import Dict, Any, List, Optional
from ai_test_automation.agents.case_parser_agent import CaseParserAgent
from ai_test_automation.agents.midscene_dsl_agent import MidsceneDSLAgent
from ai_test_automation.agents.playwright_generator_agent import PlaywrightGeneratorAgent
from ai_test_automation.agents.batch_executor_agent import BatchExecutorAgent
from ai_test_automation.agents.result_collector_agent import ResultCollectorAgent
from ai_test_automation.agents.report_generator_agent import ReportGeneratorAgent
from ai_test_automation.models import TestCase, MidsceneDSL, PlaywrightScript, ExecutionResult, TestReport
from ai_test_automation.utils.logger import app_logger
from ai_test_automation.config import settings
import asyncio
import time
from datetime import datetime


class TestAutomationPipeline:
    """测试自动化流水线"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = app_logger
        
        # 初始化各个Agent
        self.case_parser = CaseParserAgent(self.config.get('case_parser', {}))
        self.dsl_generator = MidsceneDSLAgent(self.config.get('dsl_generator', {}))
        self.script_generator = PlaywrightGeneratorAgent(self.config.get('script_generator', {}))
        self.batch_executor = BatchExecutorAgent(self.config.get('batch_executor', {}))
        self.result_collector = ResultCollectorAgent(self.config.get('result_collector', {}))
        self.report_generator = ReportGeneratorAgent(self.config.get('report_generator', {}))
        
        self.logger.info("测试自动化流水线初始化完成")
    
    async def run_full_pipeline(self, input_config: Dict[str, Any]) -> Dict[str, Any]:
        """运行完整的测试自动化流水线"""
        pipeline_id = f"pipeline_{int(time.time())}"
        start_time = datetime.now()
        
        self.logger.info(f"开始执行完整流水线，ID: {pipeline_id}")
        
        try:
            # 阶段1：用例解析
            self.logger.info("阶段1：解析测试用例")
            test_cases = await self.parse_test_cases(input_config.get('case_input', {}))
            
            # 阶段2：生成Midscene DSL
            self.logger.info("阶段2：生成Midscene DSL")
            dsl_list = await self.generate_dsl(test_cases)
            
            # 阶段3：生成Playwright脚本
            self.logger.info("阶段3：生成Playwright脚本")
            scripts = await self.generate_scripts(dsl_list)
            
            # 阶段4：批量执行测试
            self.logger.info("阶段4：批量执行测试")
            execution_results = await self.execute_tests(scripts, input_config.get('execution_config', {}))
            
            # 阶段5：收集结果
            self.logger.info("阶段5：收集执行结果")
            collection_data = await self.collect_results(execution_results)
            
            # 阶段6：生成报告
            self.logger.info("阶段6：生成测试报告")
            report_data = await self.generate_report(execution_results, collection_data, input_config.get('report_config', {}))
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            pipeline_result = {
                'pipeline_id': pipeline_id,
                'status': 'completed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'stages': {
                    'test_cases_count': len(test_cases),
                    'dsl_count': len(dsl_list),
                    'scripts_count': len(scripts),
                    'execution_results_count': len(execution_results)
                },
                'test_cases': test_cases,
                'dsl_list': dsl_list,
                'scripts': scripts,
                'execution_results': execution_results,
                'collection_data': collection_data,
                'report_data': report_data
            }
            
            self.logger.info(f"流水线执行完成，耗时: {duration:.2f}秒")
            return pipeline_result
            
        except Exception as e:
            self.logger.error(f"流水线执行失败: {e}")
            return {
                'pipeline_id': pipeline_id,
                'status': 'failed',
                'error': str(e),
                'start_time': start_time.isoformat(),
                'end_time': datetime.now().isoformat()
            }
    
    async def parse_test_cases(self, case_input: Dict[str, Any]) -> List[TestCase]:
        """解析测试用例"""
        return await self.case_parser.process(case_input)
    
    async def generate_dsl(self, test_cases: List[TestCase]) -> List[MidsceneDSL]:
        """生成Midscene DSL"""
        return await self.dsl_generator.process(test_cases)
    
    async def generate_scripts(self, dsl_list: List[MidsceneDSL]) -> List[PlaywrightScript]:
        """生成Playwright脚本"""
        return await self.script_generator.process(dsl_list)
    
    async def execute_tests(self, scripts: List[PlaywrightScript], 
                          execution_config: Dict[str, Any]) -> List[ExecutionResult]:
        """执行测试"""
        input_data = {
            'scripts': scripts,
            'batch_config': execution_config
        }
        return await self.batch_executor.process(input_data)
    
    async def collect_results(self, execution_results: List[ExecutionResult]) -> Dict[str, Any]:
        """收集结果"""
        return await self.result_collector.process(execution_results)
    
    async def generate_report(self, execution_results: List[ExecutionResult],
                            collection_data: Dict[str, Any],
                            report_config: Dict[str, Any]) -> Dict[str, Any]:
        """生成报告"""
        input_data = {
            'execution_results': execution_results,
            'collection_data': collection_data,
            'report_config': report_config
        }
        return await self.report_generator.process(input_data)
    
    async def run_partial_pipeline(self, start_stage: str, input_data: Any, 
                                 config: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行部分流水线"""
        config = config or {}
        
        if start_stage == "parse":
            return await self.parse_test_cases(input_data)
        elif start_stage == "dsl":
            return await self.generate_dsl(input_data)
        elif start_stage == "script":
            return await self.generate_scripts(input_data)
        elif start_stage == "execute":
            return await self.execute_tests(input_data, config.get('execution_config', {}))
        elif start_stage == "collect":
            return await self.collect_results(input_data)
        elif start_stage == "report":
            return await self.generate_report(
                input_data.get('execution_results', []),
                input_data.get('collection_data', {}),
                config.get('report_config', {})
            )
        else:
            raise ValueError(f"不支持的起始阶段: {start_stage}")
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """获取流水线状态"""
        return {
            'agents_status': {
                'case_parser': 'ready',
                'dsl_generator': 'ready',
                'script_generator': 'ready',
                'batch_executor': 'ready',
                'result_collector': 'ready',
                'report_generator': 'ready'
            },
            'running_tasks': len(self.batch_executor.running_tasks),
            'config': self.config
        }
    
    async def cleanup(self):
        """清理资源"""
        try:
            self.batch_executor.cleanup()
            self.logger.info("流水线资源清理完成")
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")


# 全局流水线实例
pipeline = TestAutomationPipeline()
