"""
命令行工具
"""
import typer
import asyncio
from typing import Optional
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from ai_test_automation.core.pipeline import pipeline
from ai_test_automation.utils.logger import app_logger
from ai_test_automation.config import settings

app = typer.Typer(help="AI测试自动化命令行工具")
console = Console()


@app.command()
def run(
    file_path: str = typer.Argument(..., help="测试用例文件路径"),
    file_type: Optional[str] = typer.Option("auto", help="文件类型 (excel/markdown/text/auto)"),
    sheet_name: Optional[str] = typer.Option(None, help="Excel工作表名称"),
    max_concurrent: Optional[int] = typer.Option(5, help="最大并发数"),
    retry_count: Optional[int] = typer.Option(3, help="重试次数"),
    timeout: Optional[int] = typer.Option(300, help="超时时间(秒)"),
    output_dir: Optional[str] = typer.Option(None, help="输出目录"),
    generate_pdf: bool = typer.Option(True, help="是否生成PDF报告")
):
    """运行完整的测试自动化流水线"""
    
    console.print("[bold blue]AI测试自动化流水线[/bold blue]")
    console.print(f"文件路径: {file_path}")
    console.print(f"文件类型: {file_type}")
    
    # 准备配置
    case_input = {
        "file_path": file_path,
        "file_type": file_type
    }
    if sheet_name:
        case_input["sheet_name"] = sheet_name
    
    execution_config = {
        "max_concurrent": max_concurrent,
        "retry_count": retry_count,
        "timeout": timeout
    }
    
    report_config = {
        "generate_pdf": generate_pdf
    }
    if output_dir:
        report_config["output_dir"] = output_dir
    
    input_config = {
        "case_input": case_input,
        "execution_config": execution_config,
        "report_config": report_config
    }
    
    # 运行流水线
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("执行流水线...", total=None)
        
        try:
            result = asyncio.run(pipeline.run_full_pipeline(input_config))
            
            if result.get("status") == "completed":
                progress.update(task, description="[green]流水线执行完成[/green]")
                
                # 显示结果摘要
                show_pipeline_result(result)
                
                console.print(f"\n[green]✓[/green] 流水线执行成功")
                console.print(f"报告目录: {result.get('report_data', {}).get('report_dir', 'N/A')}")
                
            else:
                progress.update(task, description="[red]流水线执行失败[/red]")
                console.print(f"\n[red]✗[/red] 流水线执行失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            progress.update(task, description="[red]执行异常[/red]")
            console.print(f"\n[red]✗[/red] 执行异常: {e}")


@app.command()
def parse(
    file_path: str = typer.Argument(..., help="测试用例文件路径"),
    file_type: Optional[str] = typer.Option("auto", help="文件类型"),
    sheet_name: Optional[str] = typer.Option(None, help="Excel工作表名称"),
    output: Optional[str] = typer.Option(None, help="输出文件路径")
):
    """仅解析测试用例"""
    
    console.print("[bold blue]解析测试用例[/bold blue]")
    
    case_input = {
        "file_path": file_path,
        "file_type": file_type
    }
    if sheet_name:
        case_input["sheet_name"] = sheet_name
    
    try:
        test_cases = asyncio.run(pipeline.parse_test_cases(case_input))
        
        console.print(f"\n[green]✓[/green] 成功解析 {len(test_cases)} 个测试用例")
        
        # 显示用例列表
        table = Table(title="测试用例列表")
        table.add_column("序号", style="cyan")
        table.add_column("用例标题", style="magenta")
        table.add_column("步骤数", style="green")
        table.add_column("优先级", style="yellow")
        
        for i, case in enumerate(test_cases, 1):
            table.add_row(
                str(i),
                case.title[:50] + "..." if len(case.title) > 50 else case.title,
                str(len(case.steps)),
                case.priority or "medium"
            )
        
        console.print(table)
        
        # 保存结果
        if output:
            from ai_test_automation.utils.file_handler import FileHandler
            case_data = [case.dict() for case in test_cases]
            FileHandler.write_json(case_data, output)
            console.print(f"\n结果已保存到: {output}")
            
    except Exception as e:
        console.print(f"\n[red]✗[/red] 解析失败: {e}")


@app.command()
def status():
    """显示系统状态"""
    
    console.print("[bold blue]系统状态[/bold blue]")
    
    status_info = pipeline.get_pipeline_status()
    
    # 显示Agent状态
    table = Table(title="Agent状态")
    table.add_column("Agent", style="cyan")
    table.add_column("状态", style="green")
    
    for agent_name, agent_status in status_info["agents_status"].items():
        table.add_row(agent_name, agent_status)
    
    console.print(table)
    
    # 显示配置信息
    console.print(f"\n[bold]配置信息:[/bold]")
    console.print(f"输出目录: {settings.output_dir}")
    console.print(f"临时目录: {settings.temp_dir}")
    console.print(f"最大并发: {settings.max_concurrent_tests}")
    console.print(f"超时时间: {settings.test_timeout}秒")
    console.print(f"重试次数: {settings.retry_count}")
    
    # 显示运行中的任务
    running_tasks = status_info.get("running_tasks", 0)
    console.print(f"\n运行中的任务: {running_tasks}")


@app.command()
def config():
    """显示配置信息"""
    
    console.print("[bold blue]配置信息[/bold blue]")
    
    config_table = Table(title="当前配置")
    config_table.add_column("配置项", style="cyan")
    config_table.add_column("值", style="green")
    
    config_items = [
        ("输出目录", settings.output_dir),
        ("临时目录", settings.temp_dir),
        ("最大并发数", str(settings.max_concurrent_tests)),
        ("超时时间", f"{settings.test_timeout}秒"),
        ("重试次数", str(settings.retry_count)),
        ("浏览器类型", settings.browser_type),
        ("无头模式", str(settings.headless)),
        ("日志级别", settings.log_level),
        ("日志文件", settings.log_file)
    ]
    
    for item, value in config_items:
        config_table.add_row(item, value)
    
    console.print(config_table)


def show_pipeline_result(result: dict):
    """显示流水线执行结果"""
    
    stages = result.get("stages", {})
    
    # 显示阶段统计
    stages_table = Table(title="执行阶段统计")
    stages_table.add_column("阶段", style="cyan")
    stages_table.add_column("数量", style="green")
    
    stages_table.add_row("解析用例", str(stages.get("test_cases_count", 0)))
    stages_table.add_row("生成DSL", str(stages.get("dsl_count", 0)))
    stages_table.add_row("生成脚本", str(stages.get("scripts_count", 0)))
    stages_table.add_row("执行结果", str(stages.get("execution_results_count", 0)))
    
    console.print(stages_table)
    
    # 显示执行结果统计
    execution_results = result.get("execution_results", [])
    if execution_results:
        from ai_test_automation.models import TestCaseStatus
        
        passed = len([r for r in execution_results if r.status == TestCaseStatus.PASSED])
        failed = len([r for r in execution_results if r.status == TestCaseStatus.FAILED])
        error = len([r for r in execution_results if r.status == TestCaseStatus.ERROR])
        
        results_table = Table(title="执行结果统计")
        results_table.add_column("状态", style="cyan")
        results_table.add_column("数量", style="green")
        results_table.add_column("百分比", style="yellow")
        
        total = len(execution_results)
        results_table.add_row("通过", str(passed), f"{passed/total*100:.1f}%")
        results_table.add_row("失败", str(failed), f"{failed/total*100:.1f}%")
        results_table.add_row("错误", str(error), f"{error/total*100:.1f}%")
        results_table.add_row("总计", str(total), "100.0%")
        
        console.print(results_table)


if __name__ == "__main__":
    app()
