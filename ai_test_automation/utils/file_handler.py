"""
文件处理工具
"""
import os
import json
import pandas as pd
from typing import List, Dict, Any, Union
from pathlib import Path
import markdown
from bs4 import BeautifulSoup
from ai_test_automation.utils.logger import app_logger


class FileHandler:
    """文件处理器"""
    
    @staticmethod
    def read_excel(file_path: str, sheet_name: str = None) -> pd.DataFrame:
        """读取Excel文件"""
        try:
            if sheet_name:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
            else:
                df = pd.read_excel(file_path)
            app_logger.info(f"成功读取Excel文件: {file_path}")
            return df
        except Exception as e:
            app_logger.error(f"读取Excel文件失败: {file_path}, 错误: {e}")
            raise
    
    @staticmethod
    def read_text(file_path: str, encoding: str = 'utf-8') -> str:
        """读取文本文件"""
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            app_logger.info(f"成功读取文本文件: {file_path}")
            return content
        except Exception as e:
            app_logger.error(f"读取文本文件失败: {file_path}, 错误: {e}")
            raise
    
    @staticmethod
    def read_markdown(file_path: str, encoding: str = 'utf-8') -> Dict[str, Any]:
        """读取Markdown文件并解析"""
        try:
            content = FileHandler.read_text(file_path, encoding)
            html = markdown.markdown(content, extensions=['tables', 'fenced_code'])
            soup = BeautifulSoup(html, 'html.parser')
            
            # 解析表格
            tables = []
            for table in soup.find_all('table'):
                table_data = []
                headers = [th.get_text().strip() for th in table.find_all('th')]
                for row in table.find_all('tr')[1:]:  # 跳过表头
                    row_data = [td.get_text().strip() for td in row.find_all('td')]
                    if row_data:
                        table_data.append(dict(zip(headers, row_data)))
                tables.append(table_data)
            
            app_logger.info(f"成功解析Markdown文件: {file_path}")
            return {
                'content': content,
                'html': html,
                'tables': tables
            }
        except Exception as e:
            app_logger.error(f"解析Markdown文件失败: {file_path}, 错误: {e}")
            raise
    
    @staticmethod
    def write_json(data: Union[Dict, List], file_path: str, encoding: str = 'utf-8'):
        """写入JSON文件"""
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding=encoding) as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            app_logger.info(f"成功写入JSON文件: {file_path}")
        except Exception as e:
            app_logger.error(f"写入JSON文件失败: {file_path}, 错误: {e}")
            raise
    
    @staticmethod
    def read_json(file_path: str, encoding: str = 'utf-8') -> Union[Dict, List]:
        """读取JSON文件"""
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                data = json.load(f)
            app_logger.info(f"成功读取JSON文件: {file_path}")
            return data
        except Exception as e:
            app_logger.error(f"读取JSON文件失败: {file_path}, 错误: {e}")
            raise
    
    @staticmethod
    def write_text(content: str, file_path: str, encoding: str = 'utf-8'):
        """写入文本文件"""
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            app_logger.info(f"成功写入文本文件: {file_path}")
        except Exception as e:
            app_logger.error(f"写入文本文件失败: {file_path}, 错误: {e}")
            raise
    
    @staticmethod
    def ensure_directory(directory: str):
        """确保目录存在"""
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    @staticmethod
    def get_file_extension(file_path: str) -> str:
        """获取文件扩展名"""
        return Path(file_path).suffix.lower()
    
    @staticmethod
    def is_file_exists(file_path: str) -> bool:
        """检查文件是否存在"""
        return Path(file_path).exists()
    
    @staticmethod
    def list_files(directory: str, pattern: str = "*") -> List[str]:
        """列出目录下的文件"""
        try:
            path = Path(directory)
            files = list(path.glob(pattern))
            return [str(f) for f in files if f.is_file()]
        except Exception as e:
            app_logger.error(f"列出文件失败: {directory}, 错误: {e}")
            return []
