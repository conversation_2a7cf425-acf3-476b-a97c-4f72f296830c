"""
数据模型定义
"""
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime


class TestCaseStatus(str, Enum):
    """测试用例状态"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


class ActionType(str, Enum):
    """动作类型"""
    AI_QUERY = "aiQuery"
    AI_ASSERT = "aiAssert"
    AI_INPUT = "aiInput"
    AI_TAP = "aiTap"
    AI_SCROLL = "aiScroll"
    AI_WAIT_FOR = "aiWaitFor"


class TestStep(BaseModel):
    """测试步骤"""
    step_id: str = Field(..., description="步骤ID")
    action_type: ActionType = Field(..., description="动作类型")
    description: str = Field(..., description="步骤描述")
    target: Optional[str] = Field(None, description="目标元素")
    value: Optional[str] = Field(None, description="输入值")
    expected_result: Optional[str] = Field(None, description="预期结果")
    timeout: Optional[int] = Field(None, description="超时时间(毫秒)")
    options: Optional[Dict[str, Any]] = Field(None, description="额外选项")


class TestCase(BaseModel):
    """测试用例"""
    case_id: str = Field(..., description="用例ID")
    title: str = Field(..., description="用例标题")
    description: Optional[str] = Field(None, description="用例描述")
    url: Optional[str] = Field(None, description="测试URL")
    preconditions: Optional[List[str]] = Field(None, description="前置条件")
    steps: List[TestStep] = Field(..., description="测试步骤")
    expected_results: Optional[List[str]] = Field(None, description="预期结果")
    tags: Optional[List[str]] = Field(None, description="标签")
    priority: Optional[str] = Field("medium", description="优先级")
    status: TestCaseStatus = Field(TestCaseStatus.PENDING, description="状态")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class MidsceneDSL(BaseModel):
    """Midscene DSL结构"""
    case_id: str = Field(..., description="用例ID")
    title: str = Field(..., description="用例标题")
    setup: Optional[Dict[str, Any]] = Field(None, description="设置")
    actions: List[Dict[str, Any]] = Field(..., description="动作列表")
    assertions: List[Dict[str, Any]] = Field(..., description="断言列表")


class PlaywrightScript(BaseModel):
    """Playwright脚本"""
    case_id: str = Field(..., description="用例ID")
    title: str = Field(..., description="用例标题")
    script_content: str = Field(..., description="脚本内容")
    imports: List[str] = Field(..., description="导入语句")
    setup_code: Optional[str] = Field(None, description="设置代码")
    test_code: str = Field(..., description="测试代码")


class ExecutionResult(BaseModel):
    """执行结果"""
    case_id: str = Field(..., description="用例ID")
    status: TestCaseStatus = Field(..., description="执行状态")
    start_time: datetime = Field(..., description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    duration: Optional[float] = Field(None, description="执行时长(秒)")
    error_message: Optional[str] = Field(None, description="错误信息")
    screenshots: List[str] = Field(default_factory=list, description="截图路径")
    videos: List[str] = Field(default_factory=list, description="视频路径")
    logs: List[str] = Field(default_factory=list, description="日志信息")
    retry_count: int = Field(0, description="重试次数")


class TestReport(BaseModel):
    """测试报告"""
    report_id: str = Field(..., description="报告ID")
    title: str = Field(..., description="报告标题")
    total_cases: int = Field(..., description="总用例数")
    passed_cases: int = Field(..., description="通过用例数")
    failed_cases: int = Field(..., description="失败用例数")
    skipped_cases: int = Field(..., description="跳过用例数")
    error_cases: int = Field(..., description="错误用例数")
    pass_rate: float = Field(..., description="通过率")
    start_time: datetime = Field(..., description="开始时间")
    end_time: datetime = Field(..., description="结束时间")
    duration: float = Field(..., description="总时长(秒)")
    results: List[ExecutionResult] = Field(..., description="执行结果列表")
    created_at: datetime = Field(default_factory=datetime.now)


class BatchExecution(BaseModel):
    """批量执行配置"""
    batch_id: str = Field(..., description="批次ID")
    case_ids: List[str] = Field(..., description="用例ID列表")
    max_concurrent: int = Field(5, description="最大并发数")
    retry_count: int = Field(3, description="重试次数")
    timeout: int = Field(300, description="超时时间(秒)")
    browser_config: Optional[Dict[str, Any]] = Field(None, description="浏览器配置")
    created_at: datetime = Field(default_factory=datetime.now)
