from dataclasses import dataclass
from typing import Optional
import os

@dataclass
class ConverterConfig:
    """转换器配置类"""
    
    # AI 配置
    model_name: str = "qwen-vl-max-latest"
    api_key: str = ""
    base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    # 输出配置
    output_dir: str = "output"
    tests_dir: str = "output/tests"
    reports_dir: str = "output/reports"
    templates_dir: str = "templates"
    
    # 执行配置
    timeout: int = 30000
    parallel_limit: int = 3
    
    # Playwright 配置
    headless: bool = True
    viewport_width: int = 1280
    viewport_height: int = 720
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.api_key:
            self.api_key = os.getenv("QWEN_API_KEY", "")
        
        # 创建输出目录
        os.makedirs(self.tests_dir, exist_ok=True)
        os.makedirs(self.reports_dir, exist_ok=True)