"""
测试转换器项目结构
"""

class TestConverterProject:
    """
    项目目录结构：
    
    test_converter/
    ├── agents/                    # Agent 模块
    │   ├── parser_agent.py       # 测试用例解析器
    │   ├── structure_agent.py    # 脚本结构生成器  
    │   ├── command_agent.py      # Midscene指令转换器
    │   ├── data_agent.py         # 测试数据管理器
    │   └── optimizer_agent.py    # 脚本优化器
    ├── core/                     # 核心模块
    │   ├── converter.py          # 主转换器
    │   ├── executor.py           # 批量执行器
    │   └── reporter.py           # 报告生成器
    ├── templates/                # 模板文件
    │   ├── playwright_base.ts    # Playwright基础模板
    │   └── midscene_fixtures.ts  # Midscene fixture模板
    ├── output/                   # 输出目录
    │   ├── tests/               # 生成的测试脚本
    │   └── reports/             # 测试报告
    └── config/                  # 配置文件
        └── converter_config.py  # 转换器配置
    """