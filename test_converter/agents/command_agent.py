from typing import List, Dict

class MidsceneCommandAgent:
    """Midscene指令转换器 Agent"""
    
    def __init__(self, config):
        self.config = config
    
    def convert(self, steps: List[Dict]) -> List[str]:
        """将测试步骤转换为Midscene指令"""
        commands = []
        
        for i, step in enumerate(steps):
            command = self._convert_single_step(step, i)
            if command:
                commands.append(command)
        
        return commands
    
    def _convert_single_step(self, step: Dict, index: int) -> str:
        """转换单个测试步骤"""
        step_type = step.get('type', 'action')
        description = step.get('description', '')
        selector = step.get('selector', '')
        value = step.get('value', '')
        expected = step.get('expected', '')
        
        # 添加注释
        comment = f"  // 步骤 {index + 1}: {description}"
        
        if step_type == 'navigate':
            url = step.get('value', step.get('url', ''))
            command = f"  await page.goto('{url}');"
            
        elif step_type == 'input':
            if selector and value:
                command = f"  await aiInput('{selector}', '{value}');"
            else:
                command = f"  await ai('在{selector or '输入框'}中输入{value}');"
                
        elif step_type == 'click':
            if selector:
                command = f"  await aiTap('{selector}');"
            else:
                command = f"  await aiTap('{description}');"
                
        elif step_type == 'assertion':
            if expected:
                command = f"  await aiAssert('{expected}');"
            else:
                command = f"  await aiAssert('{description}');"
                
        elif step_type == 'action':
            # 复合操作，使用通用ai指令
            command = f"  await ai('{description}');"
            
        else:
            # 默认使用ai指令
            command = f"  await ai('{description}');"
        
        return f"{comment}\n{command}"
    
    def generate_additional_commands(self, parsed_case: Dict) -> List[str]:
        """生成额外的辅助指令"""
        commands = []
        
        # 添加等待网络空闲
        commands.append("  // 等待页面加载完成")
        commands.append("  await page.waitForLoadState('networkidle');")
        
        # 如果有测试数据，添加数据验证
        test_data = parsed_case.get('test_data', {})
        if test_data.get('variables'):
            commands.append("  // 验证测试数据")
            for key, value in test_data['variables'].items():
                commands.append(f"  // {key}: {value}")
        
        return commands