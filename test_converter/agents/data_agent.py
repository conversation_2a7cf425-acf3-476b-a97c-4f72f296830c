import re
from typing import Dict, List, Any

class TestDataAgent:
    """测试数据管理器 Agent"""
    
    def __init__(self, config):
        self.config = config
    
    def extract_data(self, parsed_case: Dict) -> Dict[str, Any]:
        """提取和处理测试数据"""
        test_data = parsed_case.get('test_data', {})
        
        # 从步骤中提取数据
        extracted_data = self._extract_from_steps(parsed_case.get('steps', []))
        
        # 合并数据
        variables = {**test_data.get('variables', {}), **extracted_data['variables']}
        datasets = test_data.get('datasets', []) + extracted_data['datasets']
        
        return {
            'variables': variables,
            'datasets': datasets,
            'parameterized': len(datasets) > 0
        }
    
    def _extract_from_steps(self, steps: List[Dict]) -> Dict[str, Any]:
        """从测试步骤中提取数据"""
        variables = {}
        datasets = []
        
        for step in steps:
            # 提取输入值
            if step.get('type') == 'input' and step.get('value'):
                key = step.get('selector', f"input_{len(variables)}")
                variables[key] = step['value']
            
            # 提取URL参数
            if step.get('type') == 'navigate' and step.get('value'):
                url = step['value']
                url_params = self._extract_url_params(url)
                variables.update(url_params)
            
            # 提取描述中的变量（如：用户名"admin"，密码"123456"）
            description = step.get('description', '')
            desc_vars = self._extract_variables_from_text(description)
            variables.update(desc_vars)
        
        return {
            'variables': variables,
            'datasets': datasets
        }
    
    def _extract_url_params(self, url: str) -> Dict[str, str]:
        """从URL中提取参数"""
        params = {}
        
        # 提取查询参数
        if '?' in url:
            query_part = url.split('?')[1]
            for param in query_part.split('&'):
                if '=' in param:
                    key, value = param.split('=', 1)
                    params[f"url_{key}"] = value
        
        return params
    
    def _extract_variables_from_text(self, text: str) -> Dict[str, str]:
        """从文本中提取变量"""
        variables = {}
        
        # 匹配引号中的内容
        quotes_pattern = r'["\']([^"\']+)["\']'
        matches = re.findall(quotes_pattern, text)
        
        for i, match in enumerate(matches):
            variables[f"text_var_{i}"] = match
        
        # 匹配特定模式（如：用户名admin，密码123456）
        patterns = [
            r'用户名[：:]?\s*([^\s，,]+)',
            r'密码[：:]?\s*([^\s，,]+)',
            r'邮箱[：:]?\s*([^\s，,]+)',
            r'手机[：:]?\s*([^\s，,]+)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                field_name = pattern.split('[')[0]
                variables[field_name] = matches[0]
        
        return variables
    
    def generate_data_setup(self, test_data: Dict[str, Any]) -> str:
        """生成测试数据设置代码"""
        if not test_data.get('variables'):
            return ""
        
        setup_code = "  // 测试数据设置\n"
        for key, value in test_data['variables'].items():
            setup_code += f"  const {key} = '{value}';\n"
        
        return setup_code