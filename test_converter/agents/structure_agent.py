from typing import Dict
from pathlib import Path

class ScriptStructureAgent:
    """脚本结构生成器 Agent"""
    
    def __init__(self, config):
        self.config = config
        self.template_dir = Path(config.templates_dir)
    
    def generate(self, parsed_case: Dict, test_name: str) -> str:
        """生成测试脚本结构"""
        
        # 读取基础模板
        template = self._load_template()
        
        # 生成导入语句
        imports = self._generate_imports()
        
        # 生成测试配置
        test_config = self._generate_test_config(parsed_case)
        
        # 生成beforeEach钩子
        before_each = self._generate_before_each(parsed_case.get('target_url', ''))
        
        # 生成测试函数框架
        test_function = self._generate_test_function(test_name, parsed_case)
        
        return f"{imports}\n\n{test_config}\n\n{before_each}\n\n{test_function}"
    
    def _load_template(self) -> str:
        """加载基础模板"""
        template_path = self.template_dir / "playwright_base.ts"
        if template_path.exists():
            return template_path.read_text(encoding='utf-8')
        return ""
    
    def _generate_imports(self) -> str:
        """生成导入语句"""
        return """import { test } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { PlaywrightAiFixture } from '@midscene/web/playwright';"""
    
    def _generate_test_config(self, parsed_case: Dict) -> str:
        """生成测试配置"""
        return f"""const testWithAI = test.extend<PlayWrightAiFixtureType>(PlaywrightAiFixture({{
  waitForNetworkIdleTimeout: 2000,
}}));"""
    
    def _generate_before_each(self, target_url: str) -> str:
        """生成beforeEach钩子"""
        if not target_url:
            target_url = "https://www.baidu.com"
            
        return f"""testWithAI.beforeEach(async ({{ page }}) => {{
  await page.goto('{target_url}');
  await page.waitForLoadState('networkidle');
}});"""
    
    def _generate_test_function(self, test_name: str, parsed_case: Dict) -> str:
        """生成测试函数框架"""
        title = parsed_case.get('title', test_name)
        description = parsed_case.get('description', '')
        
        return f"""testWithAI('{title}', async ({{
  ai,
  aiInput,
  aiTap,
  aiScroll,
  aiAssert,
  aiQuery,
  aiWaitFor,
  aiHover,
  aiKeyboardPress,
  page
}}) => {{
  // {description}
  
  // 测试步骤将在这里插入
  {{MIDSCENE_COMMANDS}}
}});"""