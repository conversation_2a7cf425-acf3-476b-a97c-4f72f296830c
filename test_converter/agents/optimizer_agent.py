import re
from typing import List, Dict, Any

class ScriptOptimizerAgent:
    """脚本优化器 Agent"""
    
    def __init__(self, config):
        self.config = config
    
    def optimize(self, script_structure: str, commands: List[str], 
                 test_data: Dict[str, Any]) -> str:
        """优化测试脚本"""
        
        # 1. 插入测试数据设置
        data_setup = self._generate_data_setup(test_data)
        
        # 2. 优化命令序列
        optimized_commands = self._optimize_commands(commands)
        
        # 3. 添加错误处理
        error_handling = self._add_error_handling(optimized_commands)
        
        # 4. 合并所有内容
        final_commands = data_setup + "\n" + "\n".join(error_handling)
        
        # 5. 替换占位符
        final_script = script_structure.replace("{MIDSCENE_COMMANDS}", final_commands)
        
        # 6. 格式化代码
        formatted_script = self._format_code(final_script)
        
        return formatted_script
    
    def _generate_data_setup(self, test_data: Dict[str, Any]) -> str:
        """生成测试数据设置"""
        if not test_data.get('variables'):
            return ""
        
        setup = "  // 测试数据设置"
        for key, value in test_data['variables'].items():
            clean_key = re.sub(r'[^\w]', '_', key)
            setup += f"\n  const {clean_key} = '{value}';"
        
        return setup
    
    def _optimize_commands(self, commands: List[str]) -> List[str]:
        """优化命令序列"""
        optimized = []
        
        for i, command in enumerate(commands):
            # 移除重复的等待命令
            if 'waitForLoadState' in command:
                if i == 0 or 'waitForLoadState' not in commands[i-1]:
                    optimized.append(command)
            else:
                optimized.append(command)
        
        # 在关键操作后添加等待
        final_optimized = []
        for command in optimized:
            final_optimized.append(command)
            
            # 在导航后添加等待
            if 'page.goto' in command:
                final_optimized.append("  await page.waitForLoadState('networkidle');")
            
            # 在点击后添加短暂等待
            elif 'aiTap' in command and 'button' in command.lower():
                final_optimized.append("  await page.waitForTimeout(1000);")
        
        return final_optimized
    
    def _add_error_handling(self, commands: List[str]) -> List[str]:
        """添加错误处理"""
        enhanced_commands = []
        
        enhanced_commands.append("  try {")
        
        for command in commands:
            if command.strip():
                enhanced_commands.append("  " + command)
        
        enhanced_commands.extend([
            "  } catch (error) {",
            "    console.error('测试执行失败:', error);",
            "    throw error;",
            "  }"
        ])
        
        return enhanced_commands
    
    def _format_code(self, script: str) -> str:
        """格式化代码"""
        # 移除多余的空行
        lines = script.split('\n')
        formatted_lines = []
        prev_empty = False
        
        for line in lines:
            is_empty = not line.strip()
            if not (is_empty and prev_empty):
                formatted_lines.append(line)
            prev_empty = is_empty
        
        return '\n'.join(formatted_lines)