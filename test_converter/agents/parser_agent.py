import json
import re
from typing import Dict, List
from openai import OpenAI

class TestCaseParserAgent:
    """测试用例解析器 Agent"""
    
    def __init__(self, config):
        self.config = config
        self.client = OpenAI(
            api_key=config.api_key,
            base_url=config.base_url
        )
    
    def parse(self, test_case: str) -> Dict:
        """解析自然语言测试用例"""
        prompt = f"""
        请解析以下测试用例，提取关键信息并返回JSON格式：

        测试用例：{test_case}

        请按以下格式返回：
        {{
            "title": "测试标题",
            "description": "测试描述", 
            "target_url": "目标URL（如果有）",
            "preconditions": ["前置条件1", "前置条件2"],
            "steps": [
                {{
                    "type": "action|assertion|input|click|navigate",
                    "description": "步骤描述",
                    "selector": "元素选择器（如果需要）",
                    "value": "输入值（如果是输入操作）",
                    "expected": "预期结果（如果是断言）"
                }}
            ],
            "test_data": {{
                "variables": {{}},
                "datasets": []
            }}
        }}
        
        注意：
        - type 字段必须是：action, assertion, input, click, navigate 之一
        - 对于点击操作使用 click 类型
        - 对于输入操作使用 input 类型  
        - 对于验证操作使用 assertion 类型
        - 对于页面跳转使用 navigate 类型
        - 对于复合操作使用 action 类型
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content
            # 提取JSON部分
            json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                raise ValueError("无法提取JSON格式的解析结果")
                
        except Exception as e:
            print(f"解析测试用例失败: {e}")
            return self._get_default_structure(test_case)
    
    def _get_default_structure(self, test_case: str) -> Dict:
        """返回默认的测试用例结构"""
        return {
            "title": "自动生成测试",
            "description": test_case,
            "target_url": "https://www.baidu.com",
            "preconditions": [],
            "steps": [
                {
                    "type": "action",
                    "description": test_case,
                    "selector": "",
                    "value": "",
                    "expected": ""
                }
            ],
            "test_data": {
                "variables": {},
                "datasets": []
            }
        }