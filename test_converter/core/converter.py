from typing import List, Dict, Any
from pathlib import Path
import json

from agents.parser_agent import TestCaseParserAgent
from agents.structure_agent import ScriptStructureAgent
from agents.command_agent import MidsceneCommandAgent
from agents.data_agent import TestDataAgent
from agents.optimizer_agent import ScriptOptimizerAgent

class TestCaseConverter:
    """测试用例转换器主类"""
    
    def __init__(self, config):
        self.config = config
        self.parser = TestCaseParserAgent(config)
        self.structure_gen = ScriptStructureAgent(config)
        self.command_gen = MidsceneCommandAgent(config)
        self.data_manager = TestDataAgent(config)
        self.optimizer = ScriptOptimizerAgent(config)
    
    def convert_test_case(self, test_case: str, test_name: str) -> Dict[str, Any]:
        """转换单个测试用例"""
        print(f"开始转换测试用例: {test_name}")
        
        try:
            # Step 1: 解析测试用例
            print("  1. 解析测试用例...")
            parsed_case = self.parser.parse(test_case)
            
            # Step 2: 生成脚本结构
            print("  2. 生成脚本结构...")
            script_structure = self.structure_gen.generate(parsed_case, test_name)
            
            # Step 3: 转换为Midscene指令
            print("  3. 转换Midscene指令...")
            midscene_commands = self.command_gen.convert(parsed_case['steps'])
            
            # Step 4: 处理测试数据
            print("  4. 处理测试数据...")
            test_data = self.data_manager.extract_data(parsed_case)
            
            # Step 5: 生成最终脚本
            print("  5. 优化脚本...")
            final_script = self.optimizer.optimize(
                script_structure, midscene_commands, test_data
            )
            
            print(f"  ✅ 转换完成: {test_name}")
            
            return {
                'success': True,
                'script': final_script,
                'parsed_case': parsed_case,
                'test_data': test_data,
                'file_name': f"{test_name}.spec.ts"
            }
            
        except Exception as e:
            print(f"  ❌ 转换失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'test_name': test_name
            }
    
    def batch_convert(self, test_cases: List[Dict]) -> List[Dict]:
        """批量转换测试用例"""
        print(f"开始批量转换 {len(test_cases)} 个测试用例...")
        
        results = []
        for i, case in enumerate(test_cases, 1):
            print(f"\n进度: {i}/{len(test_cases)}")
            
            test_name = case.get('name', f'test_{i}')
            test_content = case.get('content', case.get('description', ''))
            
            result = self.convert_test_case(test_content, test_name)
            results.append(result)
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        print(f"\n批量转换完成: {success_count}/{len(test_cases)} 成功")
        
        return results
    
    def save_scripts(self, conversion_results: List[Dict]) -> List[str]:
        """保存生成的脚本文件"""
        saved_files = []
        tests_dir = Path(self.config.tests_dir)
        tests_dir.mkdir(parents=True, exist_ok=True)
        
        for result in conversion_results:
            if result['success']:
                file_path = tests_dir / result['file_name']
                
                try:
                    file_path.write_text(result['script'], encoding='utf-8')
                    saved_files.append(str(file_path))
                    print(f"保存脚本: {file_path}")
                except Exception as e:
                    print(f"保存脚本失败 {file_path}: {e}")
        
        return saved_files
    
    def save_conversion_report(self, conversion_results: List[Dict]) -> str:
        """保存转换报告"""
        reports_dir = Path(self.config.reports_dir)
        reports_dir.mkdir(parents=True, exist_ok=True)
        
        report_file = reports_dir / "conversion_report.json"
        
        report_data = {
            'total_cases': len(conversion_results),
            'successful_conversions': sum(1 for r in conversion_results if r['success']),
            'failed_conversions': sum(1 for r in conversion_results if not r['success']),
            'results': conversion_results
        }
        
        try:
            report_file.write_text(
                json.dumps(report_data, indent=2, ensure_ascii=False),
                encoding='utf-8'
            )
            print(f"转换报告已保存: {report_file}")
            return str(report_file)
        except Exception as e:
            print(f"保存转换报告失败: {e}")
            return ""