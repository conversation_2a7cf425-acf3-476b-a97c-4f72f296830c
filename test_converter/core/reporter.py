import json
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

class TestReportGenerator:
    """测试报告生成器"""
    
    def __init__(self, config):
        self.config = config
        self.reports_dir = Path(config.reports_dir)
    
    def generate_comprehensive_report(self, conversion_results: List[Dict], 
                                    execution_results: Dict[str, Any]) -> str:
        """生成综合测试报告"""
        
        report_data = {
            "generated_at": datetime.now().isoformat(),
            "conversion_summary": self._analyze_conversion_results(conversion_results),
            "execution_summary": execution_results.get("summary", {}),
            "detailed_results": self._combine_results(conversion_results, execution_results),
            "recommendations": self._generate_recommendations(conversion_results, execution_results)
        }
        
        # 生成Markdown报告
        markdown_report = self._generate_markdown_report(report_data)
        
        # 保存报告
        report_file = self.reports_dir / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        report_file.write_text(markdown_report, encoding='utf-8')
        
        # 保存JSON数据
        json_file = self.reports_dir / f"test_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        json_file.write_text(json.dumps(report_data, indent=2, ensure_ascii=False), encoding='utf-8')
        
        print(f"综合报告已生成: {report_file}")
        return str(report_file)
    
    def _analyze_conversion_results(self, conversion_results: List[Dict]) -> Dict[str, Any]:
        """分析转换结果"""
        total = len(conversion_results)
        successful = sum(1 for r in conversion_results if r.get('success', False))
        failed = total - successful
        
        # 分析失败原因
        failure_reasons = {}
        for result in conversion_results:
            if not result.get('success', False):
                error = result.get('error', 'Unknown error')
                failure_reasons[error] = failure_reasons.get(error, 0) + 1
        
        return {
            "total_cases": total,
            "successful_conversions": successful,
            "failed_conversions": failed,
            "success_rate": (successful / total * 100) if total > 0 else 0,
            "failure_reasons": failure_reasons
        }
    
    def _combine_results(self, conversion_results: List[Dict], 
                        execution_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """合并转换和执行结果"""
        combined = []
        execution_data = execution_results.get("results", {})
        
        for conv_result in conversion_results:
            if conv_result.get('success', False):
                test_name = conv_result.get('file_name', '')
                
                # 查找对应的执行结果
                exec_result = None
                for file_path, result in execution_data.items():
                    if test_name in file_path:
                        exec_result = result
                        break
                
                combined.append({
                    "test_name": conv_result.get('test_name', test_name),
                    "conversion_success": True,
                    "execution_result": exec_result,
                    "overall_success": exec_result.get('success', False) if exec_result else False
                })
            else:
                combined.append({
                    "test_name": conv_result.get('test_name', 'Unknown'),
                    "conversion_success": False,
                    "conversion_error": conv_result.get('error', ''),
                    "execution_result": None,
                    "overall_success": False
                })
        
        return combined
    
    def _generate_recommendations(self, conversion_results: List[Dict], 
                                execution_results: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        conv_summary = self._analyze_conversion_results(conversion_results)
        exec_summary = execution_results.get("summary", {})
        
        # 转换相关建议
        if conv_summary["success_rate"] < 80:
            recommendations.append("转换成功率较低，建议优化测试用例的描述格式和结构")
        
        if conv_summary.get("failure_reasons"):
            most_common_error = max(conv_summary["failure_reasons"].items(), key=lambda x: x[1])
            recommendations.append(f"最常见的转换错误是: {most_common_error[0]}，建议重点优化")
        
        # 执行相关建议
        if exec_summary.get("success_rate", 0) < 70:
            recommendations.append("测试执行成功率较低，建议检查页面元素定位和等待策略")
        
        if exec_summary.get("average_duration", 0) > 30:
            recommendations.append("平均执行时间较长，建议优化等待时间和并发设置")
        
        # 通用建议
        recommendations.extend([
            "建议定期更新AI模型以提高转换准确性",
            "考虑添加更多的错误处理和重试机制",
            "建议建立测试用例标准化模板"
        ])
        
        return recommendations
    
    def _generate_markdown_report(self, report_data: Dict[str, Any]) -> str:
        """生成Markdown格式报告"""
        
        conv_summary = report_data["conversion_summary"]
        exec_summary = report_data["execution_summary"]
        detailed_results = report_data["detailed_results"]
        recommendations = report_data["recommendations"]
        
        report = f"""# 测试转换与执行报告

生成时间: {report_data["generated_at"]}

## 📊 执行概览

### 转换结果
- **总测试用例数**: {conv_summary["total_cases"]}
- **转换成功**: {conv_summary["successful_conversions"]} ✅
- **转换失败**: {conv_summary["failed_conversions"]} ❌
- **转换成功率**: {conv_summary["success_rate"]:.1f}%

### 执行结果
- **总测试数**: {exec_summary.get("total_tests", 0)}
- **执行通过**: {exec_summary.get("passed", 0)} ✅
- **执行失败**: {exec_summary.get("failed", 0)} ❌
- **执行成功率**: {exec_summary.get("success_rate", 0):.1f}%
- **总执行时间**: {exec_summary.get("total_duration", 0):.2f}秒
- **平均执行时间**: {exec_summary.get("average_duration", 0):.2f}秒

## 📋 详细结果

| 测试名称 | 转换状态 | 执行状态 | 执行时间 | 备注 |
|---------|---------|---------|---------|------|
"""
        
        for result in detailed_results:
            test_name = result["test_name"]
            conv_status = "✅" if result["conversion_success"] else "❌"
            
            exec_result = result.get("execution_result")
            if exec_result:
                exec_status = "✅" if exec_result.get("success", False) else "❌"
                duration = f"{exec_result.get('duration', 0):.2f}s"
                note = exec_result.get("error", "") if not exec_result.get("success", False) else ""
            else:
                exec_status = "⏸️"
                duration = "-"
                note = result.get("conversion_error", "转换失败")
            
            report += f"| {test_name} | {conv_status} | {exec_status} | {duration} | {note} |\n"
        
        # 添加失败原因分析
        if conv_summary.get("failure_reasons"):
            report += "\n## 🔍 转换失败原因分析\n\n"
            for reason, count in conv_summary["failure_reasons"].items():
                report += f"- **{reason}**: {count} 次\n"
        
        # 添加改进建议
        report += "\n## 💡 改进建议\n\n"
        for i, recommendation in enumerate(recommendations, 1):
            report += f"{i}. {recommendation}\n"
        
        # 添加技术信息
        report += f"""
## 🔧 技术信息

- **AI模型**: {self.config.model_name}
- **并发限制**: {self.config.parallel_limit}
- **超时设置**: {self.config.timeout}ms
- **无头模式**: {self.config.headless}

---
*报告由 Midscene 测试转换器自动生成*
"""
        
        return report