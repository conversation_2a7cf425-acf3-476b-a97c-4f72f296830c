import subprocess
import asyncio
import json
from pathlib import Path
from typing import List, Dict, Any
import time

class BatchTestExecutor:
    """批量测试执行器"""
    
    def __init__(self, config):
        self.config = config
        self.output_dir = Path(config.output_dir)
        self.reports_dir = Path(config.reports_dir)
    
    async def execute_tests(self, test_files: List[str]) -> Dict[str, Any]:
        """批量执行测试"""
        print(f"开始执行 {len(test_files)} 个测试文件...")
        
        start_time = time.time()
        results = {}
        
        # 限制并发数量
        semaphore = asyncio.Semaphore(self.config.parallel_limit)
        
        async def run_with_semaphore(test_file):
            async with semaphore:
                return await self.run_single_test(test_file)
        
        # 并发执行测试
        tasks = [run_with_semaphore(test_file) for test_file in test_files]
        test_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 整理结果
        for test_file, result in zip(test_files, test_results):
            if isinstance(result, Exception):
                results[test_file] = {
                    "success": False,
                    "error": str(result),
                    "exit_code": -1,
                    "duration": 0
                }
            else:
                results[test_file] = result
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 生成执行摘要
        summary = self._generate_execution_summary(results, total_duration)
        
        return {
            "summary": summary,
            "results": results,
            "total_duration": total_duration
        }
    
    async def run_single_test(self, test_file: str) -> Dict[str, Any]:
        """执行单个测试文件"""
        print(f"执行测试: {Path(test_file).name}")
        
        start_time = time.time()
        
        # 构建Playwright命令
        cmd = [
            "npx", "playwright", "test",
            test_file,
            "--reporter=json",
            f"--timeout={self.config.timeout}"
        ]
        
        if self.config.headless:
            cmd.append("--headed=false")
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=Path.cwd()
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=self.config.timeout / 1000 + 30  # 额外30秒缓冲
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 解析测试结果
            test_result = self._parse_test_output(stdout.decode(), stderr.decode())
            
            return {
                "success": process.returncode == 0,
                "exit_code": process.returncode,
                "duration": duration,
                "stdout": stdout.decode(),
                "stderr": stderr.decode(),
                "parsed_result": test_result
            }
            
        except asyncio.TimeoutError:
            print(f"测试超时: {test_file}")
            return {
                "success": False,
                "exit_code": -1,
                "duration": self.config.timeout / 1000,
                "error": "测试执行超时",
                "stdout": "",
                "stderr": "Timeout"
            }
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"测试执行异常: {test_file} - {e}")
            return {
                "success": False,
                "exit_code": -1,
                "duration": duration,
                "error": str(e),
                "stdout": "",
                "stderr": str(e)
            }
    
    def _parse_test_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """解析测试输出"""
        try:
            # 尝试解析JSON格式的输出
            if stdout.strip():
                json_data = json.loads(stdout)
                return {
                    "format": "json",
                    "data": json_data
                }
        except json.JSONDecodeError:
            pass
        
        # 解析文本格式输出
        return {
            "format": "text",
            "stdout_lines": stdout.split('\n'),
            "stderr_lines": stderr.split('\n'),
            "has_errors": len(stderr.strip()) > 0
        }
    
    def _generate_execution_summary(self, results: Dict[str, Any], 
                                   total_duration: float) -> Dict[str, Any]:
        """生成执行摘要"""
        total_tests = len(results)
        passed_tests = sum(1 for r in results.values() if r.get('success', False))
        failed_tests = total_tests - passed_tests
        
        avg_duration = sum(r.get('duration', 0) for r in results.values()) / total_tests if total_tests > 0 else 0
        
        return {
            "total_tests": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "total_duration": total_duration,
            "average_duration": avg_duration
        }
    
    def run_playwright_merge_reports(self, blob_reports_dir: str) -> bool:
        """合并Playwright报告"""
        try:
            cmd = [
                "npx", "playwright", "merge-reports",
                "--reporter", "html",
                blob_reports_dir
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"合并报告失败: {e}")
            return False