"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/8/7 16:21
*  @Project :   ai-test
*  @FileName:   mid_config.py
*  @description: 
**************************************
"""
import os
from typing import Optional, Dict, Any


class MidsceneConfig:
    """Midscene 配置类"""
    
    def __init__(
        self,
        server_url: str = "http://localhost:5800",
        openai_api_key: Optional[str] = None,
        openai_base_url: str = "https://api.openai.com/v1",
        model_name: str = "gpt-4o",
        use_qwen_vl: bool = False,
        qwen_api_key: Optional[str] = None,
        qwen_base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1",
        timeout: int = 60,
        cache_enabled: bool = True,
        deep_think: bool = False
    ):
        self.server_url = server_url
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        self.openai_base_url = openai_base_url
        self.model_name = model_name
        self.use_qwen_vl = use_qwen_vl
        self.qwen_api_key = qwen_api_key or os.getenv("QWEN_API_KEY")
        self.qwen_base_url = qwen_base_url
        self.timeout = timeout
        self.cache_enabled = cache_enabled
        self.deep_think = deep_think
    
    def to_env_dict(self) -> Dict[str, str]:
        """转换为环境变量字典"""
        env_dict = {}
        
        if self.openai_api_key:
            env_dict["OPENAI_API_KEY"] = self.openai_api_key
        
        if self.openai_base_url:
            env_dict["OPENAI_BASE_URL"] = self.openai_base_url
            
        if self.model_name:
            env_dict["MIDSCENE_MODEL_NAME"] = self.model_name
            
        if self.cache_enabled:
            env_dict["MIDSCENE_CACHE"] = "true"
            
        if self.deep_think:
            env_dict["MIDSCENE_DEEP_THINK"] = "true"
            
        return env_dict
